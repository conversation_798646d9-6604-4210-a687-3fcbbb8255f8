from datetime import datetime
import json
from typing import Dict, Any, List, Optional

from peewee import (
    <PERSON>, CharField, IntegerField, BooleanField, DateTimeField, 
    ForeignKeyField, TextField, AutoField
)
from playhouse.postgres_ext import (
    PostgresqlExtDatabase, ArrayField, JSONField, BinaryJSONField
)

# Database configuration
db = PostgresqlExtDatabase(
    'database_name',  # Replace with your database name
    user='username',      # Replace with your username
    password='password',  # Replace with your password
    host='localhost',     # Replace with your host
    port=5432            # Replace with your port
)

class BaseModel(Model):
    """Base model class that should be inherited by all models."""
    
    class Meta:
        database = db


# Many-to-many relationship tables
class VideoTag(BaseModel):
    """Many-to-many relationship between Video and Tag."""
    
    class Meta:
        table_name = 'video_tag'
        indexes = (
            (('video_id', 'tag_name'), True),  # Unique index
        )
    
    video = ForeignKeyField(column_name='video_id', field='id')  # Will be defined after Video class
    tag = ForeignKeyField(column_name='tag_name', field='name')  # Will be defined after Tag class


class VideoCategory(BaseModel):
    """Many-to-many relationship between Video and Category."""
    
    class Meta:
        table_name = 'video_category'
        indexes = (
            (('video_id', 'category_name'), True),  # Unique index
        )
    
    video = ForeignKeyField(column_name='video_id', field='id')  # Will be defined after Video class
    category = ForeignKeyField(column_name='category_name', field='name')  # Will be defined after Category class


class Tag(BaseModel):
    """Tag model corresponding to the Tag model in Prisma."""
    
    class Meta:
        table_name = 'tag'
    
    name = CharField(primary_key=True)
    link = CharField()
    
    # Define the many-to-many relationship with Video
    videos = None  # Will be populated after Video class is defined


class Category(BaseModel):
    """Category model corresponding to the Category model in Prisma."""
    
    class Meta:
        table_name = 'category'
    
    name = CharField(primary_key=True)
    link = CharField()
    
    # Define the many-to-many relationship with Video
    videos = None  # Will be populated after Video class is defined


class Video(BaseModel):
    """Video model corresponding to the Video model in Prisma."""
    
    class Meta:
        table_name = 'video'
    
    id = AutoField()
    viewkey = CharField(unique=True)
    entry_created_at = DateTimeField(default=datetime.now)
    entry_updated_at = DateTimeField()
    video_uploaded_at = DateTimeField()
    url = CharField(unique=True)
    title = CharField()
    likes = IntegerField(default=0)
    views = IntegerField(default=0)
    processed = BooleanField(default=False)
    favorited = BooleanField(default=False)
    dislikes = IntegerField(default=0)
    duration = IntegerField()
    metadata = BinaryJSONField(null=True)  # Using BinaryJSONField for better performance
    comments = ArrayField(TextField)  # Using ArrayField for string array
    
    # Define the many-to-many relationships
    tags = None  # Will be populated below
    categories = None  # Will be populated below
    
    def save(self, *args, **kwargs):
        self.entry_updated_at = datetime.now()
        return super(Video, self).save(*args, **kwargs)


class Performer(BaseModel):
    """Performer model corresponding to the Performer model in Prisma."""
    
    class Meta:
        table_name = 'performer'
    
    id = AutoField()
    created_at = DateTimeField(default=datetime.now)
    updated_at = DateTimeField()
    performer_added_at = DateTimeField()
    name = CharField()
    bio = TextField()
    performer_type = CharField()
    pornhub_url = CharField()
    avatar = CharField()
    desc = TextField(null=True)
    gender = CharField(null=True)
    ethnicity = CharField(null=True)
    fake_boobs = BooleanField(null=True)
    hair_color = CharField(null=True)
    height = CharField(null=True)
    interested_in = CharField(null=True)
    interests_and_hobbies = TextField(null=True)
    piercings = BooleanField(null=True)
    weight = CharField(null=True)
    relationship_status = CharField(null=True)
    tattoos = BooleanField(null=True)
    turn_ons = BooleanField(null=True)
    profile_views = IntegerField(default=0)
    video_views = IntegerField(default=0)
    videos_watched = IntegerField(default=0)
    
    # Foreign key to Category
    category = ForeignKeyField(Category, backref='performers', column_name='category_name', field='name', null=True)
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.now()
        return super(Performer, self).save(*args, **kwargs)


# Set up the many-to-many relationships
Video.tags = Tag.videos = VideoTag.tag.rel_model
Video.categories = Category.videos = VideoCategory.category.rel_model

# Update the foreign key references
VideoTag.video.rel_model = Video
VideoTag.tag.rel_model = Tag
VideoCategory.video.rel_model = Video
VideoCategory.category.rel_model = Category


# Helper functions for working with many-to-many relationships
def get_video_tags(video: Video) -> List[Tag]:
    """Get all tags for a video."""
    query = (Tag
             .select()
             .join(VideoTag)
             .join(Video)
             .where(Video.id == video.id))
    return list(query)


def get_video_categories(video: Video) -> List[Category]:
    """Get all categories for a video."""
    query = (Category
             .select()
             .join(VideoCategory)
             .join(Video)
             .where(Video.id == video.id))
    return list(query)


def add_tag_to_video(video: Video, tag: Tag) -> None:
    """Add a tag to a video."""
    try:
        VideoTag.create(video=video, tag=tag)
    except:
        # Tag already exists for this video
        pass


def add_category_to_video(video: Video, category: Category) -> None:
    """Add a category to a video."""
    try:
        VideoCategory.create(video=video, category=category)
    except:
        # Category already exists for this video
        pass


def remove_tag_from_video(video: Video, tag: Tag) -> None:
    """Remove a tag from a video."""
    query = VideoTag.delete().where(
        (VideoTag.video == video) & 
        (VideoTag.tag == tag)
    )
    query.execute()


def remove_category_from_video(video: Video, category: Category) -> None:
    """Remove a category from a video."""
    query = VideoCategory.delete().where(
        (VideoCategory.video == video) & 
        (VideoCategory.category == category)
    )
    query.execute()


# Create tables
def create_tables():
    """Create all tables in the database."""
    with db:
        db.create_tables([
            Video, 
            Tag, 
            Category, 
            Performer, 
            VideoTag, 
            VideoCategory
        ])


# Example usage
if __name__ == "__main__":
    # Connect to the database
    db.connect()
    
    # Create tables
    create_tables()
    
    # Create a tag
    tag = Tag.create(name="example_tag", link="https://example.com/tags/example_tag")
    
    # Create a category
    category = Category.create(name="example_category", link="https://example.com/categories/example_category")
    
    # Create a video
    video = Video.create(
        viewkey="abc123",
        video_uploaded_at=datetime.now(),
        url="https://example.com/videos/abc123",
        title="Example Video",
        duration=300,
        comments=["Great video!", "Nice content"],
        metadata={"source": "example.com", "quality": "HD"}
    )
    
    # Add tag and category to video
    add_tag_to_video(video, tag)
    add_category_to_video(video, category)
    
    # Create a performer
    performer = Performer.create(
        performer_added_at=datetime.now(),
        name="Example Performer",
        bio="Example bio",
        performer_type="actor",
        pornhub_url="https://example.com/performers/example",
        avatar="https://example.com/avatars/example.jpg",
        category=category
    )
    
    # Close the database connection
    db.close()
