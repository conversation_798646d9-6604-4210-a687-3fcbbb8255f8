#!/usr/bin/env python3
"""
Utility functions for working with the database models.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from models import (
    db, Video, Tag, Category, Performer, VideoTag, VideoCategory,
    add_tag_to_video, add_category_to_video, remove_tag_from_video,
    remove_category_from_video, get_video_tags, get_video_categories
)


class VideoManager:
    """Manager class for Video operations."""
    
    @staticmethod
    def create_video(
        viewkey: str,
        url: str,
        title: str,
        duration: int,
        video_uploaded_at: datetime,
        comments: List[str] = None,
        metadata: Dict[str, Any] = None,
        tags: List[str] = None,
        categories: List[str] = None,
        likes: int = 0,
        views: int = 0,
        dislikes: int = 0,
        processed: bool = False,
        favorited: bool = False
    ) -> Video:
        """
        Create a new video with associated tags and categories.
        
        Args:
            viewkey: Unique identifier for the video
            url: URL of the video
            title: Title of the video
            duration: Duration of the video in seconds
            video_uploaded_at: When the video was uploaded
            comments: List of comments on the video
            metadata: Additional metadata for the video
            tags: List of tag names to associate with the video
            categories: List of category names to associate with the video
            likes: Number of likes
            views: Number of views
            dislikes: Number of dislikes
            processed: Whether the video has been processed
            favorited: Whether the video has been favorited
            
        Returns:
            The created Video instance
        """
        with db.atomic():
            # Create the video
            video = Video.create(
                viewkey=viewkey,
                url=url,
                title=title,
                duration=duration,
                video_uploaded_at=video_uploaded_at,
                comments=comments or [],
                metadata=metadata or {},
                likes=likes,
                views=views,
                dislikes=dislikes,
                processed=processed,
                favorited=favorited
            )
            
            # Add tags
            if tags:
                for tag_name in tags:
                    tag, _ = Tag.get_or_create(
                        name=tag_name,
                        defaults={"link": f"https://example.com/tags/{tag_name}"}
                    )
                    add_tag_to_video(video, tag)
            
            # Add categories
            if categories:
                for category_name in categories:
                    category, _ = Category.get_or_create(
                        name=category_name,
                        defaults={"link": f"https://example.com/categories/{category_name}"}
                    )
                    add_category_to_video(video, category)
            
            return video
    
    @staticmethod
    def get_video_by_viewkey(viewkey: str) -> Optional[Video]:
        """Get a video by its viewkey."""
        try:
            return Video.get(Video.viewkey == viewkey)
        except Video.DoesNotExist:
            return None
    
    @staticmethod
    def get_video_by_url(url: str) -> Optional[Video]:
        """Get a video by its URL."""
        try:
            return Video.get(Video.url == url)
        except Video.DoesNotExist:
            return None
    
    @staticmethod
    def get_videos_by_tag(tag_name: str) -> List[Video]:
        """Get all videos with a specific tag."""
        return list(Video
                   .select()
                   .join(VideoTag)
                   .join(Tag)
                   .where(Tag.name == tag_name))
    
    @staticmethod
    def get_videos_by_category(category_name: str) -> List[Video]:
        """Get all videos in a specific category."""
        return list(Video
                   .select()
                   .join(VideoCategory)
                   .join(Category)
                   .where(Category.name == category_name))
    
    @staticmethod
    def update_video_metadata(video: Video, metadata: Dict[str, Any]) -> Video:
        """Update a video's metadata."""
        # Merge existing metadata with new metadata
        if video.metadata:
            video.metadata.update(metadata)
        else:
            video.metadata = metadata
        
        video.save()
        return video
    
    @staticmethod
    def add_comment_to_video(video: Video, comment: str) -> Video:
        """Add a comment to a video."""
        if not video.comments:
            video.comments = []
        
        video.comments.append(comment)
        video.save()
        return video


class PerformerManager:
    """Manager class for Performer operations."""
    
    @staticmethod
    def create_performer(
        name: str,
        bio: str,
        performer_type: str,
        pornhub_url: str,
        avatar: str,
        performer_added_at: datetime,
        category_name: Optional[str] = None,
        desc: Optional[str] = None,
        gender: Optional[str] = None,
        ethnicity: Optional[str] = None,
        fake_boobs: Optional[bool] = None,
        hair_color: Optional[str] = None,
        height: Optional[str] = None,
        interested_in: Optional[str] = None,
        interests_and_hobbies: Optional[str] = None,
        piercings: Optional[bool] = None,
        weight: Optional[str] = None,
        relationship_status: Optional[str] = None,
        tattoos: Optional[bool] = None,
        turn_ons: Optional[bool] = None,
        profile_views: int = 0,
        video_views: int = 0,
        videos_watched: int = 0
    ) -> Performer:
        """
        Create a new performer.
        
        Args:
            name: Name of the performer
            bio: Biography of the performer
            performer_type: Type of performer
            pornhub_url: URL of the performer's profile
            avatar: URL of the performer's avatar
            performer_added_at: When the performer was added
            category_name: Name of the category to associate with the performer
            desc: Description of the performer
            gender: Gender of the performer
            ethnicity: Ethnicity of the performer
            fake_boobs: Whether the performer has fake boobs
            hair_color: Hair color of the performer
            height: Height of the performer
            interested_in: What the performer is interested in
            interests_and_hobbies: Interests and hobbies of the performer
            piercings: Whether the performer has piercings
            weight: Weight of the performer
            relationship_status: Relationship status of the performer
            tattoos: Whether the performer has tattoos
            turn_ons: Turn-ons of the performer
            profile_views: Number of profile views
            video_views: Number of video views
            videos_watched: Number of videos watched
            
        Returns:
            The created Performer instance
        """
        with db.atomic():
            # Get or create the category if provided
            category = None
            if category_name:
                category, _ = Category.get_or_create(
                    name=category_name,
                    defaults={"link": f"https://example.com/categories/{category_name}"}
                )
            
            # Create the performer
            performer = Performer.create(
                name=name,
                bio=bio,
                performer_type=performer_type,
                pornhub_url=pornhub_url,
                avatar=avatar,
                performer_added_at=performer_added_at,
                category=category,
                desc=desc,
                gender=gender,
                ethnicity=ethnicity,
                fake_boobs=fake_boobs,
                hair_color=hair_color,
                height=height,
                interested_in=interested_in,
                interests_and_hobbies=interests_and_hobbies,
                piercings=piercings,
                weight=weight,
                relationship_status=relationship_status,
                tattoos=tattoos,
                turn_ons=turn_ons,
                profile_views=profile_views,
                video_views=video_views,
                videos_watched=videos_watched
            )
            
            return performer
    
    @staticmethod
    def get_performers_by_category(category_name: str) -> List[Performer]:
        """Get all performers in a specific category."""
        return list(Performer
                   .select()
                   .join(Category)
                   .where(Category.name == category_name))
    
    @staticmethod
    def increment_profile_views(performer: Performer) -> Performer:
        """Increment the profile views for a performer."""
        performer.profile_views += 1
        performer.save()
        return performer
    
    @staticmethod
    def increment_video_views(performer: Performer) -> Performer:
        """Increment the video views for a performer."""
        performer.video_views += 1
        performer.save()
        return performer
    
    @staticmethod
    def increment_videos_watched(performer: Performer) -> Performer:
        """Increment the videos watched for a performer."""
        performer.videos_watched += 1
        performer.save()
        return performer


class TagManager:
    """Manager class for Tag operations."""
    
    @staticmethod
    def create_tag(name: str, link: Optional[str] = None) -> Tag:
        """Create a new tag."""
        if not link:
            link = f"https://example.com/tags/{name}"
        
        return Tag.create(name=name, link=link)
    
    @staticmethod
    def get_or_create_tag(name: str, link: Optional[str] = None) -> Tag:
        """Get or create a tag."""
        if not link:
            link = f"https://example.com/tags/{name}"
        
        tag, _ = Tag.get_or_create(name=name, defaults={"link": link})
        return tag
    
    @staticmethod
    def get_all_tags() -> List[Tag]:
        """Get all tags."""
        return list(Tag.select())
    
    @staticmethod
    def get_tag_by_name(name: str) -> Optional[Tag]:
        """Get a tag by its name."""
        try:
            return Tag.get(Tag.name == name)
        except Tag.DoesNotExist:
            return None


class CategoryManager:
    """Manager class for Category operations."""
    
    @staticmethod
    def create_category(name: str, link: Optional[str] = None) -> Category:
        """Create a new category."""
        if not link:
            link = f"https://example.com/categories/{name}"
        
        return Category.create(name=name, link=link)
    
    @staticmethod
    def get_or_create_category(name: str, link: Optional[str] = None) -> Category:
        """Get or create a category."""
        if not link:
            link = f"https://example.com/categories/{name}"
        
        category, _ = Category.get_or_create(name=name, defaults={"link": link})
        return category
    
    @staticmethod
    def get_all_categories() -> List[Category]:
        """Get all categories."""
        return list(Category.select())
    
    @staticmethod
    def get_category_by_name(name: str) -> Optional[Category]:
        """Get a category by its name."""
        try:
            return Category.get(Category.name == name)
        except Category.DoesNotExist:
            return None


# Example usage
if __name__ == "__main__":
    # Connect to the database
    db.connect()
    
    try:
        # Create a video with tags and categories
        video = VideoManager.create_video(
            viewkey="example123",
            url="https://example.com/videos/example123",
            title="Example Video",
            duration=300,
            video_uploaded_at=datetime.now(),
            comments=["Great video!"],
            metadata={"source": "example.com"},
            tags=["tag1", "tag2"],
            categories=["category1"]
        )
        
        # Create a performer
        performer = PerformerManager.create_performer(
            name="Example Performer",
            bio="Example bio",
            performer_type="actor",
            pornhub_url="https://example.com/performers/example",
            avatar="https://example.com/avatars/example.jpg",
            performer_added_at=datetime.now(),
            category_name="category1"
        )
        
        print(f"Created video: {video.title}")
        print(f"Created performer: {performer.name}")
        
    finally:
        # Close the database connection
        db.close()
