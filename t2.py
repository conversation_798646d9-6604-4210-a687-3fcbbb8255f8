from pprint import pprint

# from pornhub_api import PornhubApi

import phub
import requests

# from phub.locals import *
client = phub.Client()
# api = PornhubApi()
import xxxparser.pornhub as ph

if __name__ == "__main__":
    vid_id = "67d97daf5daa0"
    sesh = ph.login()
    # query = "anal+cosplay+teen+gape"
    query=""
    videos = []
    tags = ["anal", "cosplay", "teen", "gape", "redhead"]

    for i in range(1, 10):
        url = f"https://www.pornhub.com/webmasters/search?search={query}&tags[]={','.join(tags)}&ordering=featured&thumbsize=small&page={i}"

        # url = f"https://www.pornhub.com/webmasters/search?search={query}&ordering=newest&thumbsize=small&page={i}"
        # api.search.search_videos(ordering="mostviewed", tags=tags, category=category)
        response = requests.get(url)

        if response.status_code == 200:
            json_data = response.json()
            for entry in json_data["videos"]:
                videos.append({
                    "url" : entry["url"],
                    "title": entry["title"],
                    "metadata": {}
                })

    # links = [
    #
    #
    # ]
    # ph.parse_pornhub_url()
    # parsed_page = ph.parse_pornhub_url(sesh, url=vid["page"],domain="www.pornhub.com")
    # pprint(videos)

    # videos = ph.search_videos(sesh, query, DEBUG=True)
    # pprint(videos)
    # vid = ph.get_video_info(sesh, "https://www.pornhub.com/view_video.php?viewkey=67d97daf5daa0")
    # pprint(vid)
    # parsed_page = ph.parse_pornhub_url(sesh, url=vid["page"],domain="www.pornhub.com")
    # pprint(parsed_page)
    # video = client.get("67d97daf5daa0")
    # performers = video.pornstars
    # videos = api.search.search_videos(
    #     "chechick",
    #     ordering="mostviewed",
    #     period="weekly",
    #     tags=["black"],
    # )
    # vid = api.video.get_by_id("6454e728862fd")
    # pprint(performers)
    # pprint(video.dictify())
    # pprint(video.author.dictify())
    # pprint(video.dictify())
    # for vid in videos:
    #     pprint(vid.dict())
    #     # print(vid.title, vid.video_id)