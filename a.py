import csv

def extract_first_100_lines(input_path, output_path):
    with open(input_path, 'r', newline='', encoding='utf-8') as infile, \
            open(output_path, 'w', newline='', encoding='utf-8') as outfile:

        reader = csv.reader(infile)
        writer = csv.writer(outfile)

        for i, row in enumerate(reader):
            writer.writerow(row)
            if i >= 100:
                break

    print(f"Extracted first 100 lines (including header) to: {output_path}")


if __name__=="__main__":
    # Example usage
    extract_first_100_lines('C:/temp/pornhub.com-db/pornhub.com-db.csv', 'C:/temp/pornhub.com-db/first_100_lines.csv')