import av
import subprocess

from imageio_ffmpeg import get_ffmpeg_exe
from pymediainfo import MediaInfo
from hachoir.parser import createParser
from hachoir.metadata import extractMetadata
from hachoir.core import config as HachoirConfig

# Suppress warnings
import warnings

from logger import safe_logger

warnings.filterwarnings("ignore", category=UserWarning)
HachoirConfig.quiet = True


class BaseValidator:
    def validate(self, file_path):
        raise NotImplementedError("Subclasses must implement validate method.")


class PyAVValidator(BaseValidator):
    def validate(self, file_path):
        try:
            with av.open(file_path) as container:
                video_streams = [s for s in container.streams if s.type == "video"]
                audio_streams = [s for s in container.streams if s.type == "audio"]
                if video_streams or audio_streams:
                    return True, None
                return False, "No playable video or audio streams found."
        except Exception as e:
            return False, f"PyAV Error: {file_path} : {e}"


class HachoirValidator(BaseValidator):
    def validate(self, file_path):
        parser = None
        try:
            # Create a parser for the file
            parser = createParser(file_path)
            if not parser:
                return False, "Unable to parse file structure."

            # Extract metadata from the parser
            metadata = extractMetadata(parser)
            if metadata:
                # Check if metadata indicates playability
                return self.is_video_playable(metadata)
            else:
                # Fallback: Assume playable but log the lack of metadata
                return True, "Metadata extraction failed, but file may still play fine."
        except Exception as e:
            # Log and return as warning for minor issues
            return True, f"Hachoir Warning: {file_path} : {e}"
        finally:
            # Ensure proper cleanup of parser's stream
            self.safe_close(parser)

    def is_video_playable(self, metadata):
        """
        Determine if the video is likely playable based on extracted metadata.
        """
        if metadata.has("duration") and metadata.get("duration").value > 0:
            return True, None
        return False, "No duration or critical playback information found."

    @staticmethod
    def safe_close(parser):
        """
        Safely close the parser's stream if applicable.
        """
        if parser and hasattr(parser, "stream"):
            stream = getattr(parser, "stream", None)
            if stream and hasattr(stream, "close"):
                try:
                    stream.close()
                except Exception:
                    pass  # Ignore errors during cleanup


class MediaInfoValidator(BaseValidator):
    def validate(self, file_path):
        try:
            media_info = MediaInfo.parse(file_path)
            for track in media_info.tracks:
                if track.track_type in ("Video", "Audio") and track.duration:
                    return True, None
            return False, "No valid video or audio tracks found."
        except Exception as e:
            return False, f"MediaInfo Error: {file_path} : {e}"


class ThumbnailValidator(BaseValidator):
    def validate(self, file_path):
        try:
            ffmpeg_path = get_ffmpeg_exe()
            command = [
                ffmpeg_path, "-i", file_path,
                "-vf", "thumbnail", "-frames:v", "1", "-f", "null", "-"
            ]
            result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return (True, None) if result.returncode == 0 else (False, "Failed to generate thumbnail.")
        except Exception as e:
            return False, f"Thumbnail Error: {file_path} : {e}"


# Unified Validation Function
def validate(file_path):
    safe_logger.info(f"Starting validation for file: {file_path}")
    validators = [PyAVValidator(), HachoirValidator(), MediaInfoValidator(), ThumbnailValidator()]
    for validator in validators:
        safe_logger.info(f"Running {validator.__class__.__name__}...")
        valid, error = validator.validate(file_path)
        if not valid:
            safe_logger.error(error)
            return valid, error
    safe_logger.info(f"Validation successful for file: {file_path}")
    return True, None