// datasource db {
//   // could be postgresql or mysql
//   provider = "postgresql"
//
//   url = "file:dev.db"
// }
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator db {
  previewFeatures = ["driverAdapters", "postgresqlExtensions"]
  provider        = "prisma-client-py"

  // interface            = "asyncio"
  // recursive_type_depth = 5
}

model PornhubVideo {
  viewkey           String     @unique
  entry_created_at  DateTime   @default(now())
  entry_updated_at  DateTime   @updatedAt
  video_uploaded_at DateTime
  url               String     @unique
  title             String
  likes             Int        @default(0)
  views             Int        @default(0)
  processed         <PERSON>olean    @default(false)
  favorited         <PERSON>olean    @default(false)
  dislikes          Int        @default(0)
  duration          Int
  metadata          Json?
  tags              Tag[]
  categories        Category[]
  comments          String[]
}

model Category {
  name      String      @id @unique
  link      String
  Video     Video[]
  Performer Performer[]
}

// model Channel {
//   name      String      @id @unique
//   link      String
//   Video     Video[]
//   Performer Performer[]
// }

model Tag {
  name  String  @id @unique
  link  String
  Video Video[]
}

model Performer {
  id                    Int      @id @default(autoincrement())
  created_at            DateTime @default(now())
  updated_at            DateTime @updatedAt
  performer_added_at    DateTime
  name                  String
  bio                   String
  performer_type        String
  pornhub_url           String
  avatar                String
  desc                  String?
  gender                String?
  ethnicity             String?
  fake_boobs            Boolean?
  hair_color            String?
  height                String?
  interested_in         String?
  interests_and_hobbies String?
  piercings             Boolean?
  weight                String?
  relationship_status   String?
  tattoos               Boolean?
  turn_ons              Boolean?
  profile_views         Int      @default(0)
  video_views           Int      @default(0)
  videos_watched        Int      @default(0)

  Category     Category? @relation(fields: [categoryName], references: [name])
  categoryName String?
}
