# Peewee ORM Implementation for Prisma Schema

This is a Peewee ORM implementation based on the Prisma schema. It uses PostgreSQL with Playhouse extensions to provide similar functionality to the original Prisma schema.

## Setup

### Prerequisites

- Python 3.7+
- PostgreSQL database
- Peewee and psycopg2

### Installation

```bash
pip install peewee psycopg2-binary
```

### Database Configuration

Edit the database configuration in `models.py`:

```python
db = PostgresqlExtDatabase(
    'database_name',  # Replace with your database name
    user='username',      # Replace with your username
    password='password',  # Replace with your password
    host='localhost',     # Replace with your host
    port=5432            # Replace with your port
)
```

## Model Overview

The implementation includes the following models:

- `Video`: Stores video information
- `Tag`: Stores tag information
- `Category`: Stores category information
- `Performer`: Stores performer information
- `VideoTag`: Many-to-many relationship between Video and Tag
- `VideoCategory`: Many-to-many relationship between Video and Category

## Usage Examples

### Creating Tables

```python
from models import create_tables

# Create all tables
create_tables()
```

### Working with Videos

```python
from models import Video
from datetime import datetime

# Create a video
video = Video.create(
    viewkey="abc123",
    video_uploaded_at=datetime.now(),
    url="https://example.com/videos/abc123",
    title="Example Video",
    duration=300,
    comments=["Great video!", "Nice content"],
    metadata={"source": "example.com", "quality": "HD"}
)

# Query videos
all_videos = Video.select()
video_by_id = Video.get_by_id(1)
video_by_viewkey = Video.get(Video.viewkey == "abc123")

# Update a video
video.title = "Updated Title"
video.save()  # This automatically updates entry_updated_at

# Delete a video
video.delete_instance()
```

### Working with Tags and Categories

```python
from models import Tag, Category, add_tag_to_video, add_category_to_video, get_video_tags, get_video_categories

# Create a tag
tag = Tag.create(name="example_tag", link="https://example.com/tags/example_tag")

# Create a category
category = Category.create(name="example_category", link="https://example.com/categories/example_category")

# Add tag and category to video
add_tag_to_video(video, tag)
add_category_to_video(video, category)

# Get all tags for a video
tags = get_video_tags(video)

# Get all categories for a video
categories = get_video_categories(video)

# Remove tag from video
remove_tag_from_video(video, tag)
```

### Working with Performers

```python
from models import Performer
from datetime import datetime

# Create a performer
performer = Performer.create(
    performer_added_at=datetime.now(),
    name="Example Performer",
    bio="Example bio",
    performer_type="actor",
    pornhub_url="https://example.com/performers/example",
    avatar="https://example.com/avatars/example.jpg",
    category=category  # Foreign key to Category
)

# Query performers
all_performers = Performer.select()
performer_by_id = Performer.get_by_id(1)
performers_by_category = Performer.select().where(Performer.category == category)

# Update a performer
performer.name = "Updated Name"
performer.save()  # This automatically updates updated_at

# Delete a performer
performer.delete_instance()
```

## Differences from Prisma Schema

1. **Many-to-many relationships**: Prisma handles many-to-many relationships implicitly, while Peewee requires explicit junction tables (`VideoTag` and `VideoCategory`).

2. **Array fields**: Prisma has native support for array fields, while Peewee uses PostgreSQL's `ArrayField` extension.

3. **JSON fields**: Prisma uses `Json?` type, while Peewee uses `BinaryJSONField` from Playhouse extensions.

4. **Auto-updating fields**: Prisma has `@updatedAt` annotation, while Peewee requires overriding the `save()` method to update timestamps.

## Advanced Queries

### JSON Field Queries

```python
# Query videos with specific metadata
videos_with_hd = Video.select().where(Video.metadata['quality'].contains('HD'))
```

### Array Field Queries

```python
# Query videos with specific comments
videos_with_comment = Video.select().where(Video.comments.contains(['Great video!']))
```

### Joins and Complex Queries

```python
# Get all videos with a specific tag
videos_with_tag = (Video
                  .select()
                  .join(VideoTag)
                  .join(Tag)
                  .where(Tag.name == 'example_tag'))

# Get all performers in a specific category
performers_in_category = (Performer
                         .select()
                         .join(Category)
                         .where(Category.name == 'example_category'))
```
