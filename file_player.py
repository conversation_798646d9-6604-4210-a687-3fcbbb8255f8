import collections
import json
import os
import sys
import random
import shutil
from pathlib import Path

import vlc
import threading
from collections import deque
from queue import Queue
from time import sleep
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout,
    QHBoxLayout, QPushButton, QSlider, QLabel, QMessageBox
)
from PySide6.QtCore import Qt, QTimer, QSize, QSettings
from PySide6.QtGui import QMouseEvent, QIcon, QWheelEvent, QKeyEvent

# VLC Setup
os.add_dll_directory(r"C:\Program Files\VideoLAN\VLC")
os.environ['VLC_PLUGIN_PATH'] = r"C:\Program Files\VideoLAN\VLC\plugins"

class FilePlayer(QMainWindow):
    def __init__(self, file_list, search_root_path, must_keep_dir, should_keep_dir, min_size_mb=None):
        super().__init__()
        self.settings = QSettings("MyApp", "FilePlayer")
        size = self.settings.value("windowSize")
        if size:
            self.resize(size)
        else:
            self.resize(1024, 768)
        self.setWindowTitle("Python File Player")

        # Store search root path for relative path calculations
        self.search_root_path = Path(search_root_path)

        # Store minimum file size for filtering
        self.min_size_mb = min_size_mb

        # Create output directories if they don't exist
        try:
            self.must_keep_dir = Path(must_keep_dir)
            self.should_keep_dir = Path(should_keep_dir)
            self.must_keep_dir.mkdir(exist_ok=True)
            self.should_keep_dir.mkdir(exist_ok=True)
        except Exception as e:
            self.show_error(f"Error creating directories: {e}")

        self.instance = vlc.Instance("--quiet")
        self.mediaplayer = self.instance.media_player_new()

        # File queues
        self.file_queue = Queue()
        self.file_queue.queue = deque(file_list)
        self.buffer_queue = Queue(maxsize=10)

        # Action queues for post-processing
        self.must_keep_queue = Queue()
        self.should_keep_queue = Queue()
        self.skip_queue = Queue()
        self.delete_queue = Queue()

        self.current_file = None
        self.is_paused = False

        self.widget = QWidget(self)
        self.setCentralWidget(self.widget)
        self.vbox = QVBoxLayout(self.widget)

        self.title_label = QLabel("", self)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.vbox.addWidget(self.title_label)

        self.video_frame = QWidget(self)
        self.video_frame.setStyleSheet("background-color: black;")
        # Main layout with video player and controls
        self.main_layout = QHBoxLayout()

        # Left side with video player and playback controls
        self.left_panel = QVBoxLayout()

        # Add video frame to left panel
        self.left_panel.addWidget(self.video_frame, stretch=1)

        # Time label
        self.time_label = QLabel("00:00 / 00:00", self)
        self.time_label.setAlignment(Qt.AlignCenter)

        # Slider for seeking
        self.slider = QSlider(Qt.Horizontal)
        self.slider.setRange(0, 1000)
        self.slider.setStyleSheet("QSlider::groove:horizontal { height: 16px; } QSlider::handle:horizontal { height: 16px; width: 10px; }")

        # Playback controls
        self.controls = QHBoxLayout()
        self.play_pause_btn = QPushButton()
        self.play_pause_btn.setIcon(QIcon.fromTheme("media-playback-pause"))
        self.play_pause_btn.setIconSize(QSize(32, 32))
        self.controls.addWidget(self.play_pause_btn)

        # Right side with action buttons
        self.right_panel = QVBoxLayout()
        self.must_keep_btn = QPushButton("Must Keep")
        self.should_keep_btn = QPushButton("Should Keep")
        self.skip_btn = QPushButton("Skip")
        self.delete_btn = QPushButton("Mark for Deletion")

        # Style the buttons with fixed width
        button_style = "padding: 10px; min-width: 150px; max-width: 150px;"
        self.must_keep_btn.setStyleSheet(f"background-color: green; color: white; {button_style}")
        self.should_keep_btn.setStyleSheet(f"background-color: lightgreen; color: black; {button_style}")
        self.skip_btn.setStyleSheet(f"background-color: gray; color: white; {button_style}")
        self.delete_btn.setStyleSheet(f"background-color: red; color: white; {button_style}")

        # Removed folder action buttons to simplify the interface

        # Add buttons to right panel
        self.right_panel.addWidget(self.must_keep_btn)
        self.right_panel.addWidget(self.should_keep_btn)
        self.right_panel.addWidget(self.skip_btn)
        self.right_panel.addWidget(self.delete_btn)
        self.right_panel.addSpacing(20)

        # Folder action buttons removed
        self.right_panel.addStretch(1)

        # Add controls to left panel
        self.left_panel.addLayout(self.controls)
        self.left_panel.addWidget(self.time_label)
        self.left_panel.addWidget(self.slider)

        # Add panels to main layout
        self.main_layout.addLayout(self.left_panel, stretch=3)
        self.main_layout.addLayout(self.right_panel, stretch=1)

        # Set main layout
        self.vbox.addLayout(self.main_layout)

        self.timer = QTimer(self)
        self.timer.setInterval(1000)
        self.timer.timeout.connect(self.update_ui)

        # Connect signals
        self.play_pause_btn.clicked.connect(self.toggle_play_pause)
        self.slider.sliderMoved.connect(self.set_position)
        self.slider.sliderReleased.connect(self.set_position_on_release)

        # Connect action buttons
        self.must_keep_btn.clicked.connect(self.must_keep_action)
        self.should_keep_btn.clicked.connect(self.should_keep_action)
        self.skip_btn.clicked.connect(self.skip_action)
        self.delete_btn.clicked.connect(self.delete_action)

        # Folder action buttons removed

        self.video_frame.mouseDoubleClickEvent = self.toggle_fullscreen
        self.video_frame.wheelEvent = self.wheel_seek
        self.fullscreen = False

        if sys.platform.startswith("win"):
            self.mediaplayer.set_hwnd(int(self.video_frame.winId()))
        else:
            self.mediaplayer.set_xwindow(self.video_frame.winId())

        # Start background threads
        threading.Thread(target=self.buffer_manager, daemon=True).start()
        threading.Thread(target=self.post_processor, daemon=True).start()
        QTimer.singleShot(500, self.next_file)

    def closeEvent(self, event):
        self.settings.setValue("windowSize", self.size())
        super().closeEvent(event)

    def buffer_manager(self):
        """Load files from the file queue into the buffer queue"""
        while True:
            if self.buffer_queue.qsize() < 10 and not self.file_queue.empty():
                file_path = self.file_queue.get()
                try:
                    # Get file metadata
                    file_name = os.path.basename(file_path)
                    file_size = os.path.getsize(file_path)

                    buffer_item = {
                        'title': file_name,
                        'file_path': file_path,
                        'size': file_size,
                        'metadata': {
                            'file_name': file_name,
                            'file_size': file_size,
                            'file_path': file_path
                        }
                    }
                    self.buffer_queue.put(buffer_item)
                    print("Buffered:", file_path)
                except Exception as e:
                    print("Error buffering:", file_path, e)
            else:
                sleep(1)

    def post_processor(self):
        """Process files in the action queues"""
        while True:
            try:
                # Process must keep queue
                if not self.must_keep_queue.empty():
                    file_path = self.must_keep_queue.get()
                    try:
                        # Add a small delay to ensure file is released
                        sleep(0.2)

                        # Maintain directory structure relative to search root
                        rel_path = self.get_relative_path(file_path)
                        dest_path = self.must_keep_dir / rel_path

                        # Create parent directories if they don't exist
                        os.makedirs(os.path.dirname(dest_path), exist_ok=True)

                        # Check if file exists and is accessible
                        if os.path.exists(file_path) and os.access(file_path, os.R_OK):
                            shutil.copy2(file_path, dest_path)
                            print(f"Copied to must keep: {file_path} -> {dest_path}")
                        else:
                            print(f"File not accessible: {file_path}")
                    except Exception as e:
                        print(f"Error copying to must keep: {file_path}, {e}")

                # Process should keep queue
                if not self.should_keep_queue.empty():
                    file_path = self.should_keep_queue.get()
                    try:
                        # Add a small delay to ensure file is released
                        sleep(0.2)

                        # Maintain directory structure relative to search root
                        rel_path = self.get_relative_path(file_path)
                        dest_path = self.should_keep_dir / rel_path

                        # Create parent directories if they don't exist
                        os.makedirs(os.path.dirname(dest_path), exist_ok=True)

                        # Check if file exists and is accessible
                        if os.path.exists(file_path) and os.access(file_path, os.R_OK):
                            shutil.copy2(file_path, dest_path)
                            print(f"Copied to should keep: {file_path} -> {dest_path}")
                        else:
                            print(f"File not accessible: {file_path}")
                    except Exception as e:
                        print(f"Error copying to should keep: {file_path}, {e}")

                # Process skip queue - just remove from queue
                if not self.skip_queue.empty():
                    file_path = self.skip_queue.get()
                    print(f"Skipped: {file_path}")

                # Process delete queue
                if not self.delete_queue.empty():
                    file_path = self.delete_queue.get()
                    try:
                        # Add a small delay to ensure file is released
                        sleep(0.2)

                        # Check if file exists and is accessible
                        if os.path.exists(file_path) and os.access(file_path, os.W_OK):
                            os.remove(file_path)
                            print(f"Deleted: {file_path}")
                        else:
                            print(f"File not accessible for deletion: {file_path}")
                    except Exception as e:
                        print(f"Error deleting: {file_path}, {e}")

                # Sleep to prevent high CPU usage
                sleep(0.1)
            except Exception as e:
                print(f"Error in post_processor: {e}")
                sleep(1)  # Longer sleep on error

    def play_media(self, item):
        try:
            # Process UI events before starting
            QApplication.processEvents()

            # Check if file exists and is accessible
            if not os.path.exists(item['file_path']):
                print(f"File not found: {item['file_path']}")
                QTimer.singleShot(500, self.next_file)
                return

            if not os.access(item['file_path'], os.R_OK):
                print(f"File not readable: {item['file_path']}")
                QTimer.singleShot(500, self.next_file)
                return

            print("Playing:", item['file_path'])

            # Process UI events
            QApplication.processEvents()

            # Create new media object
            try:
                media = self.instance.media_new(
                    item['file_path'],
                    "file-caching=3000",
                    "input-fast-seek"
                )
                self.mediaplayer.set_media(media)
            except Exception as e:
                print(f"Error creating media: {e}")
                QTimer.singleShot(500, self.next_file)
                return

            # Process UI events
            QApplication.processEvents()

            # Set window handle
            try:
                if sys.platform.startswith("win"):
                    self.mediaplayer.set_hwnd(int(self.video_frame.winId()))
                else:
                    self.mediaplayer.set_xwindow(self.video_frame.winId())
            except Exception as e:
                print(f"Error setting window handle: {e}")
                QTimer.singleShot(500, self.next_file)
                return

            # Show full path in title
            self.setWindowTitle(item['file_path'])
            self.title_label.setText(item['file_path'])

            # Process UI events
            QApplication.processEvents()

            # Start playback
            try:
                self.mediaplayer.audio_set_volume(100)
                self.mediaplayer.play()
                self.is_paused = False
                self.play_pause_btn.setIcon(QIcon.fromTheme("media-playback-pause"))
                self.timer.start()

                # Store current file path only after successful playback start
                self.current_file = item['file_path']
            except Exception as e:
                print(f"Error starting playback: {e}")
                QTimer.singleShot(500, self.next_file)
                return

            # Process UI events one more time
            QApplication.processEvents()

        except Exception as e:
            print(f"Playback error: {e}")
            # Try to recover by loading next file after a delay
            QTimer.singleShot(500, self.next_file)

    def toggle_play_pause(self):
        if self.mediaplayer.is_playing():
            self.mediaplayer.pause()
            self.is_paused = True
            self.play_pause_btn.setIcon(QIcon.fromTheme("media-playback-start"))
        else:
            self.mediaplayer.set_pause(0)
            self.is_paused = False
            self.play_pause_btn.setIcon(QIcon.fromTheme("media-playback-pause"))

    def next_file(self):
        """Load the next file from the buffer queue"""
        try:
            # Clear current file reference
            self.current_file = None

            # Release the current media player
            self.unload_current_file()

            # Process UI events before continuing
            QApplication.processEvents()

            # Check if buffer queue is empty
            if self.buffer_queue.empty():
                print("Buffer queue empty, waiting for more files...")
                QTimer.singleShot(1000, self.next_file)
                return

            # Get next item from buffer queue
            item = self.buffer_queue.get()
            print(f"Loading next file: {item['file_path']}")

            # Replace immediately from file queue in a separate thread
            threading.Thread(target=self.buffer_manager, daemon=True).start()

            # Process UI events again
            QApplication.processEvents()

            # Schedule media loading with a delay
            QTimer.singleShot(500, lambda: self._load_media(item))
        except Exception as e:
            print(f"Error in next_file: {e}")
            # Try again after a delay
            QTimer.singleShot(1000, self.next_file)

    def _load_media(self, item):
        """Load media after a delay to keep UI responsive"""
        try:
            # Play the new media
            self.play_media(item)
        except Exception as e:
            print(f"Error in _load_media: {e}")
            QTimer.singleShot(1000, self.next_file)

    def unload_current_file(self):
        """Unload the current file from VLC to prevent file locking"""
        try:
            # Stop playback if playing
            if self.mediaplayer.is_playing():
                self.mediaplayer.stop()

            # Process UI events
            QApplication.processEvents()

            # Release the media player
            self.mediaplayer.release()

            # Process UI events again
            QApplication.processEvents()

            # Create a new media player instance
            self.mediaplayer = self.instance.media_player_new()

            # Set the window handle
            if sys.platform.startswith("win"):
                self.mediaplayer.set_hwnd(int(self.video_frame.winId()))
            else:
                self.mediaplayer.set_xwindow(self.video_frame.winId())

            # Force garbage collection to release file handles
            import gc
            gc.collect()

            # Small delay to ensure file is released
            sleep(0.2)

            # Process UI events one more time
            QApplication.processEvents()
        except Exception as e:
            print(f"Error unloading file: {e}")

    def must_keep_action(self):
        """Add current file to must keep queue and load next file"""
        if self.current_file:
            try:
                # Store the current file path
                current_path = self.current_file
                print(f"Processing must keep action for: {current_path}")

                # Clear current file reference immediately
                self.current_file = None

                # Stop playback immediately
                self.mediaplayer.stop()

                # Schedule the next file to be loaded after a delay
                # This ensures the UI remains responsive
                QTimer.singleShot(500, lambda: self._finish_must_keep(current_path))
            except Exception as e:
                error_msg = f"Error in must_keep_action: {e}"
                print(error_msg)
                self.show_error(error_msg)
                # Try to recover by loading next file
                QTimer.singleShot(500, self.next_file)

    def _finish_must_keep(self, file_path):
        """Complete the must keep action after a delay"""
        try:
            # Add to queue for background processing
            self.must_keep_queue.put(file_path)

            # Load the next file
            self.next_file()
        except Exception as e:
            print(f"Error in _finish_must_keep: {e}")
            QTimer.singleShot(500, self.next_file)

    def should_keep_action(self):
        """Add current file to should keep queue and load next file"""
        if self.current_file:
            try:
                # Store the current file path
                current_path = self.current_file
                print(f"Processing should keep action for: {current_path}")

                # Clear current file reference immediately
                self.current_file = None

                # Stop playback immediately
                self.mediaplayer.stop()

                # Schedule the next file to be loaded after a delay
                # This ensures the UI remains responsive
                QTimer.singleShot(500, lambda: self._finish_should_keep(current_path))
            except Exception as e:
                error_msg = f"Error in should_keep_action: {e}"
                print(error_msg)
                self.show_error(error_msg)
                # Try to recover by loading next file
                QTimer.singleShot(500, self.next_file)

    def _finish_should_keep(self, file_path):
        """Complete the should keep action after a delay"""
        try:
            # Add to queue for background processing
            self.should_keep_queue.put(file_path)

            # Load the next file
            self.next_file()
        except Exception as e:
            print(f"Error in _finish_should_keep: {e}")
            QTimer.singleShot(500, self.next_file)

    def skip_action(self):
        """Add current file to skip queue and load next file"""
        if self.current_file:
            try:
                # Store the current file path
                current_path = self.current_file
                print(f"Processing skip action for: {current_path}")

                # Clear current file reference immediately
                self.current_file = None

                # Stop playback immediately
                self.mediaplayer.stop()

                # Schedule the next file to be loaded after a delay
                # This ensures the UI remains responsive
                QTimer.singleShot(500, lambda: self._finish_skip(current_path))
            except Exception as e:
                error_msg = f"Error in skip_action: {e}"
                print(error_msg)
                self.show_error(error_msg)
                # Try to recover by loading next file
                QTimer.singleShot(500, self.next_file)

    def _finish_skip(self, file_path):
        """Complete the skip action after a delay"""
        try:
            # Add to queue for background processing
            self.skip_queue.put(file_path)

            # Load the next file
            self.next_file()
        except Exception as e:
            print(f"Error in _finish_skip: {e}")
            QTimer.singleShot(500, self.next_file)

    def delete_action(self):
        """Add current file to delete queue and load next file"""
        if self.current_file:
            try:
                # Store the current file path
                current_path = self.current_file
                print(f"Processing delete action for: {current_path}")

                # Clear current file reference immediately
                self.current_file = None

                # Stop playback immediately
                self.mediaplayer.stop()

                # Schedule the next file to be loaded after a delay
                # This ensures the UI remains responsive
                QTimer.singleShot(500, lambda: self._finish_delete(current_path))
            except Exception as e:
                error_msg = f"Error in delete_action: {e}"
                print(error_msg)
                self.show_error(error_msg)
                # Try to recover by loading next file
                QTimer.singleShot(500, self.next_file)

    def _finish_delete(self, file_path):
        """Complete the delete action after a delay"""
        try:
            # Add to queue for background processing
            self.delete_queue.put(file_path)

            # Load the next file
            self.next_file()
        except Exception as e:
            print(f"Error in _finish_delete: {e}")
            QTimer.singleShot(500, self.next_file)

    def show_error(self, message):
        """Show an error message to the user"""
        QMessageBox.critical(self, "Error", message)

    def get_relative_path(self, file_path):
        """Get path relative to search root"""
        try:
            # Convert to Path objects for reliable path handling
            path = Path(file_path)
            # Get relative path if file is under search root
            if str(path).startswith(str(self.search_root_path)):
                return path.relative_to(self.search_root_path)
            else:
                # If not under search root, just use the filename
                return path.name
        except Exception as e:
            print(f"Error getting relative path: {e}")
            return os.path.basename(file_path)

    # Folder-related methods removed to simplify the interface

    def update_ui(self):
        if self.mediaplayer.is_playing():
            pos = self.mediaplayer.get_position()
            length = self.mediaplayer.get_length()
            current = self.mediaplayer.get_time()
            if length > 0:
                self.slider.blockSignals(True)
                self.slider.setValue(int(pos * 1000))
                self.slider.blockSignals(False)
                time_str = f"{self.format_time(current)} / {self.format_time(length)}"
                self.time_label.setText(time_str)

    def format_time(self, ms):
        seconds = int(ms / 1000)
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes:02}:{seconds:02}"

    def set_position(self, position):
        self.mediaplayer.set_position(position / 1000.0)

    def set_position_on_release(self):
        position = self.slider.value()
        self.mediaplayer.set_position(position / 1000.0)

    def toggle_fullscreen(self, event: QMouseEvent):
        if self.fullscreen:
            self.showNormal()
        else:
            self.showFullScreen()
        self.fullscreen = not self.fullscreen

    def wheel_seek(self, event: QWheelEvent):
        delta = event.angleDelta().y() / 120
        current = self.mediaplayer.get_time()
        self.mediaplayer.set_time(int(current - delta * 5000))

    def keyPressEvent(self, event: QKeyEvent):
        if event.key() == Qt.Key_Right:
            self.next_file()
        elif event.key() == Qt.Key_Space:
            self.toggle_play_pause()
        # Keyboard shortcuts for actions
        elif event.key() == Qt.Key_1:
            self.must_keep_action()
        elif event.key() == Qt.Key_2:
            self.should_keep_action()
        elif event.key() == Qt.Key_3:
            self.skip_action()
        elif event.key() == Qt.Key_4:
            self.delete_action()

def get_media_files(directory, extensions=None, recursive=True, min_size_mb=None):
    """Get a list of media files from a directory

    Args:
        directory (str): Directory to search for media files
        extensions (list): List of file extensions to include
        recursive (bool): Whether to search subdirectories recursively
        min_size_mb (float): Minimum file size in megabytes (optional)

    Returns:
        list: List of file paths
    """
    if extensions is None:
        extensions = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v']

    directory_path = Path(directory)
    file_list = []
    min_size_bytes = min_size_mb * 1024 * 1024 if min_size_mb is not None else 0

    # Use os.walk for recursive directory traversal
    if recursive:
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                file_path = os.path.join(root, file)
                if any(file.lower().endswith(ext) for ext in extensions):
                    # Check file size if min_size_mb is specified
                    if min_size_mb is None or os.path.getsize(file_path) >= min_size_bytes:
                        file_list.append(file_path)
    # Use simple glob for non-recursive search
    else:
        for ext in extensions:
            for file_path in directory_path.glob(f'*{ext}'):
                file_path_str = str(file_path)
                # Check file size if min_size_mb is specified
                if min_size_mb is None or os.path.getsize(file_path_str) >= min_size_bytes:
                    file_list.append(file_path_str)

    return file_list



if __name__ == "__main__":
    # Define paths
    search_path = "C:/p2/valid/"
    must_keep_path = "C:/p2/must_keep/"
    should_keep_path = "C:/p2/should_keep/"

    # Define minimum file size (in MB)
    min_size_mb = 1024  # Set to None to include all files regardless of size


    # Get list of media files
    file_list = get_media_files(search_path, recursive=True, min_size_mb=min_size_mb)
    if not file_list:
        print(f"No media files found in {search_path}" +
              (f" with size >= {min_size_mb} MB" if min_size_mb else ""))
        sys.exit(1)

    print(f"Found {len(file_list)} media files in {search_path} and its subdirectories" +
          (f" with size >= {min_size_mb} MB" if min_size_mb else ""))

    # Shuffle the list
    random.shuffle(file_list)

    app = QApplication(sys.argv)
    player = FilePlayer(
        file_list=file_list,
        search_root_path=search_path,
        must_keep_dir=must_keep_path,
        should_keep_dir=should_keep_path,
        min_size_mb=min_size_mb
    )
    player.show()
    sys.exit(app.exec())
