import collections
import json
import os
import sys
import random
from urllib.parse import urlparse, parse_qs

import requests
import vlc
import threading
from collections import deque
import yt_dlp
from time import sleep
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout,
    QHBoxLayout, QPushButton, QSlider, QLabel
)
from PySide6.QtCore import Qt, QTimer, QSize, QSettings
from PySide6.QtGui import QMouseEvent, QIcon, QWheelEvent, QKeyEvent

# VLC Setup
os.add_dll_directory(r"C:\Program Files\VideoLAN\VLC")
os.environ['VLC_PLUGIN_PATH'] = r"C:\Program Files\VideoLAN\VLC\plugins"

class MediaPlayer(QMainWindow):
    def __init__(self, video_list):
        super().__init__()
        self.settings = QSettings("MyApp", "MediaPlayer")
        size = self.settings.value("windowSize")
        if size:
            self.resize(size)
        else:
            self.resize(1024, 768)
        self.setWindowTitle("Python Stream Player")

        self.instance = vlc.Instance("--quiet")
        self.mediaplayer = self.instance.media_player_new()

        from queue import Queue
        self.full_queue = Queue()
        self.full_queue.queue = deque(video_list)
        self.buffer_queue = Queue(maxsize=10)

        self.history = []
        self.history_index = -1
        self.is_paused = False

        self.widget = QWidget(self)
        self.setCentralWidget(self.widget)
        self.vbox = QVBoxLayout(self.widget)

        self.title_label = QLabel("", self)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.vbox.addWidget(self.title_label)

        self.video_frame = QWidget(self)
        self.video_frame.setStyleSheet("background-color: black;")
        self.vbox.addWidget(self.video_frame, stretch=1)

        self.controls = QHBoxLayout()
        self.back_btn = QPushButton()
        self.play_pause_btn = QPushButton()
        self.next_btn = QPushButton()

        self.back_btn.setIcon(QIcon.fromTheme("media-skip-backward"))
        self.play_pause_btn.setIcon(QIcon.fromTheme("media-playback-pause"))
        self.next_btn.setIcon(QIcon.fromTheme("media-skip-forward"))

        for btn in [self.back_btn, self.play_pause_btn, self.next_btn]:
            btn.setIconSize(QSize(32, 32))
            self.controls.addWidget(btn)

        self.vbox.addLayout(self.controls)

        self.time_label = QLabel("00:00 / 00:00", self)
        self.time_label.setAlignment(Qt.AlignCenter)
        self.vbox.addWidget(self.time_label)

        self.slider = QSlider(Qt.Horizontal)
        self.slider.setRange(0, 1000)
        self.slider.setStyleSheet("QSlider::groove:horizontal { height: 16px; } QSlider::handle:horizontal { height: 16px; width: 10px; }")
        self.vbox.addWidget(self.slider)

        self.timer = QTimer(self)
        self.timer.setInterval(1000)
        self.timer.timeout.connect(self.update_ui)

        self.play_pause_btn.clicked.connect(self.toggle_play_pause)
        self.back_btn.clicked.connect(self.prev_media)
        self.next_btn.clicked.connect(self.next_media)
        self.slider.sliderMoved.connect(self.set_position)
        self.slider.sliderReleased.connect(self.set_position_on_release)

        self.video_frame.mouseDoubleClickEvent = self.toggle_fullscreen
        self.video_frame.wheelEvent = self.wheel_seek
        self.fullscreen = False

        if sys.platform.startswith("win"):
            self.mediaplayer.set_hwnd(int(self.video_frame.winId()))
        else:
            self.mediaplayer.set_xwindow(self.video_frame.winId())

        threading.Thread(target=self.buffer_manager, daemon=True).start()
        QTimer.singleShot(500, self.next_media)

    def closeEvent(self, event):
        self.settings.setValue("windowSize", self.size())
        super().closeEvent(event)

    def buffer_manager(self):
        while True:
            if self.buffer_queue.qsize() < 10 and not self.full_queue.empty():
                item = self.full_queue.get()
                try:
                    ydl_opts = {
                        'format': 'best[ext=mp4]/best',
                        'quiet': True,
                        'noplaylist': True,
                        'skip_download': True
                    }
                    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                        info = ydl.extract_info(item['url'], download=False)
                        stream_url = info.get("url")
                        buffer_item = {
                            'title': info.get('title', 'Untitled'),
                            'original_url': item['url'],
                            'stream_url': stream_url,
                            'metadata': info
                        }
                        self.buffer_queue.put(buffer_item)
                        print("Buffered:", item['url'])
                except Exception as e:
                    print("Error buffering:", item['url'], e)
            else:
                sleep(1)

    def play_media(self, item):
        try:
            print("Playing:", item['stream_url'])
            media = self.instance.media_new(
                item['stream_url'],
                "network-caching=3000",
                "file-caching=3000",
                "input-fast-seek"
            )
            self.mediaplayer.set_media(media)

            if sys.platform.startswith("win"):
                self.mediaplayer.set_hwnd(int(self.video_frame.winId()))
            else:
                self.mediaplayer.set_xwindow(self.video_frame.winId())

            self.setWindowTitle(item['title'])
            self.title_label.setText(item['title'])
            self.mediaplayer.audio_set_volume(100)
            self.mediaplayer.play()
            self.is_paused = False
            self.play_pause_btn.setIcon(QIcon.fromTheme("media-playback-pause"))
            self.timer.start()
        except Exception as e:
            print("Playback error:", e)
            QTimer.singleShot(500, self.next_media)

    def toggle_play_pause(self):
        if self.mediaplayer.is_playing():
            self.mediaplayer.pause()
            self.is_paused = True
            self.play_pause_btn.setIcon(QIcon.fromTheme("media-playback-start"))
        else:
            self.mediaplayer.set_pause(0)
            self.is_paused = False
            self.play_pause_btn.setIcon(QIcon.fromTheme("media-playback-pause"))

    def prev_media(self):
        if self.history_index > 0:
            self.history_index -= 1
            item = self.history[self.history_index]
            self.mediaplayer.stop()
            self.play_media(item)

    def next_media(self):
        if self.history_index < len(self.history) - 1:
            self.history_index += 1
            item = self.history[self.history_index]
        else:
            if self.buffer_queue.empty():
                QTimer.singleShot(1000, self.next_media)
                return
            item = self.buffer_queue.get()
            self.history = self.history[:self.history_index + 1]
            self.history.append(item)
            self.history_index += 1
            # replace immediately from full queue
            threading.Thread(target=self.buffer_manager, daemon=True).start()

        self.mediaplayer.stop()
        self.play_media(item)

    def update_ui(self):
        if self.mediaplayer.is_playing():
            pos = self.mediaplayer.get_position()
            length = self.mediaplayer.get_length()
            current = self.mediaplayer.get_time()
            if length > 0:
                self.slider.blockSignals(True)
                self.slider.setValue(int(pos * 1000))
                self.slider.blockSignals(False)
                time_str = f"{self.format_time(current)} / {self.format_time(length)}"
                self.time_label.setText(time_str)

    def format_time(self, ms):
        seconds = int(ms / 1000)
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes:02}:{seconds:02}"

    def set_position(self, position):
        self.mediaplayer.set_position(position / 1000.0)

    def set_position_on_release(self):
        position = self.slider.value()
        self.mediaplayer.set_position(position / 1000.0)

    def toggle_fullscreen(self, event: QMouseEvent):
        if self.fullscreen:
            self.showNormal()
        else:
            self.showFullScreen()
        self.fullscreen = not self.fullscreen

    def wheel_seek(self, event: QWheelEvent):
        delta = event.angleDelta().y() / 120
        current = self.mediaplayer.get_time()
        self.mediaplayer.set_time(int(current - delta * 5000))

    def keyPressEvent(self, event: QKeyEvent):
        if event.key() == Qt.Key_Right:
            self.next_media()
        elif event.key() == Qt.Key_Left:
            self.prev_media()
        elif event.key() == Qt.Key_Space:
            self.toggle_play_pause()

def extract_bookmarks(node, results=None):
    if results is None:
        results = []

    if isinstance(node, dict):
        if node.get('type') == 'url':
            results.append({
                'name': node.get('name'),
                'url': node.get('url'),
                'date_added': node.get('date_added'),
                'date_last_used': node.get('date_last_used')
            })
        elif node.get('type') == 'folder':
            for child in node.get('children', []):
                extract_bookmarks(child, results)
    elif isinstance(node, list):
        for item in node:
            extract_bookmarks(item, results)

    return results




def get_bookmarks(bookmark_path):
    with open(bookmark_path, "r", encoding="utf-8") as f:
        bookmarks_data = json.load(f)
    all_bookmarks = []
    for root in ['bookmark_bar', 'other', 'synced']:
        if root in bookmarks_data.get('roots', {}):
            extract_bookmarks(bookmarks_data['roots'][root], all_bookmarks)
    return all_bookmarks




def load_bookmarks():
    # bookmarks = set()
    bookmark_data = [
        *get_bookmarks(r"C:\Users\<USER>\AppData\Local\BraveSoftware\Brave-Browser\User Data\Default\Bookmarks"),
        *get_bookmarks(r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Default\Bookmarks"),
    ]


    phub_vids = {}

    for bookmark in bookmark_data:
        parsed_url = urlparse(bookmark["url"])
        name = bookmark["name"]
        query_params = parse_qs(parsed_url.query)
        if "pornhub" in str(parsed_url.hostname).lower() and "viewkey" in query_params:
            viewkey = query_params["viewkey"][0]
            if viewkey not in phub_vids.keys():
                phub_vids[viewkey] = {
                    "url" : f"https://www.pornhub.com/view_video.php?viewkey={viewkey}",
                    "title": name,
                    "metadata": {}
                }
    print(len(phub_vids))
    vid_id_list = list(phub_vids.keys())
    # with open("PHUB_VIDS.json", "w") as file:
    #     json.dump(phub_vids, file, indent=4)
    random.shuffle(vid_id_list)
    final_list = []
    for vid_id in vid_id_list:
        final_list.append(phub_vids[vid_id])

    return final_list

import xxxparser.pornhub as ph

if __name__ == "__main__":
    # video_list = [
    #     {'url': "https://www.youtube.com/watch?v=dQw4w9WgXcQ", 'title': '', 'metadata': {}},
    #     {'url': "https://www.youtube.com/watch?v=9bZkp7q19f0", 'title': '', 'metadata': {}}
    # ]
    # video_list= []
    # sesh = ph.login()
    # query = "anal+cosplay+teen+gape"
    # # videos = ph.search_videos(sesh, query,recent=True, pages=[1, 2,3,4,5,6,7,8,9,10], DEBUG=False)
    # videos = []
    # for i in range(1, 10):
    #     url = f"https://www.pornhub.com/webmasters/search?search={query}&ordering=newest&thumbsize=small&page={i}"
    #     response = requests.get(url)
    #
    #     if response.status_code == 200:
    #         json_data = response.json()
    #         for entry in json_data["videos"]:
    #             videos.append({
    #                 "url" : entry["url"],
    #                 "title": entry["title"],
    #                 "metadata": {}
    #             })
    # video_list = videos
    # pprint(videos)
    sesh = ph.login()
    # query = "anal+cosplay+teen+gape"
    query=""
    videos = []
    tags = ["anal", "cosplay", "teen", "gape", "redhead"]

    for i in range(1, 10):
        url = f"https://www.pornhub.com/webmasters/search?search={query}&tags[]={','.join(tags)}&ordering=featured&thumbsize=small&page={i}"

        # url = f"https://www.pornhub.com/webmasters/search?search={query}&ordering=newest&thumbsize=small&page={i}"
        # api.search.search_videos(ordering="mostviewed", tags=tags, category=category)
        response = requests.get(url)

        if response.status_code == 200:
            json_data = response.json()
            for entry in json_data["videos"]:
                videos.append({
                    "url" : entry["url"],
                    "title": entry["title"],
                    "metadata": {}
                })

    video_list = videos
    app = QApplication(sys.argv)
    player = MediaPlayer(video_list)
    player.show()
    sys.exit(app.exec())
