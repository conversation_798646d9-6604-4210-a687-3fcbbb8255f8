"""
Examples showing how to use advanced SQLAlchemy features with SQLModel.
Demonstrates that SQLModel is fully compatible with SQLAlchemy functionality.
"""
from typing import Optional, List
from decimal import Decimal
from datetime import datetime
from sqlmodel import SQLModel, Field, Session, create_engine, select, text, func
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Numeric
from sqlalchemy.dialects.postgresql import TSVECTOR
from sqlalchemy.sql import func as sql_func
from sqlalchemy.orm import declarative_base


# SQLModel with advanced SQLAlchemy features
class Article(SQLModel, table=True):
    """
    Article model demonstrating advanced SQLAlchemy features in SQLModel:
    - Full-text search
    - Custom SQL functions
    - Advanced column types
    """
    __tablename__ = "articles"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    title: str = Field(max_length=200, index=True)
    content: str = Field()
    summary: Optional[str] = Field(default=None, max_length=500)
    
    # Automatic timestamps (SQLAlchemy style)
    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime, server_default=sql_func.now())
    )
    updated_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime, server_default=sql_func.now(), onupdate=sql_func.now())
    )
    
    is_published: bool = Field(default=False)
    view_count: int = Field(default=0, ge=0)
    
    # PostgreSQL full-text search vector (using sa_column for advanced types)
    search_vector: Optional[str] = Field(
        default=None,
        sa_column=Column(TSVECTOR)  # PostgreSQL specific type
    )


class Product(SQLModel, table=True):
    """Product with advanced pricing and search capabilities."""
    __tablename__ = "products_advanced"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(max_length=200, index=True)
    description: str = Field()
    price: Decimal = Field(decimal_places=2, max_digits=10)
    
    # JSON field (PostgreSQL)
    metadata_json: Optional[dict] = Field(
        default=None,
        sa_column=Column("metadata", Text)  # Store as JSON text
    )
    
    # Custom SQL column with check constraint
    discount_percentage: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=100.0,
        sa_column=Column("discount_percentage", Numeric(5, 2))
    )
    
    created_at: datetime = Field(default_factory=datetime.utcnow)


# Database setup
DATABASE_URL = "postgresql://username:password@localhost/database"
# For demo purposes, we'll use SQLite
DATABASE_URL = "sqlite:///./sqlmodel_advanced.db"

engine = create_engine(DATABASE_URL, echo=True)


def create_tables():
    """Create all tables."""
    SQLModel.metadata.create_all(engine)


# Advanced SQLAlchemy queries with SQLModel
class AdvancedQueries:
    """Examples of advanced SQLAlchemy features with SQLModel."""
    
    @staticmethod
    def full_text_search_postgresql(session: Session, search_term: str) -> List[Article]:
        """
        Full-text search using PostgreSQL's built-in capabilities.
        This uses raw SQLAlchemy functionality within SQLModel.
        """
        # Using SQLAlchemy's text() for raw SQL
        statement = select(Article).where(
            text("search_vector @@ plainto_tsquery(:search_term)")
        ).params(search_term=search_term)
        
        return session.exec(statement).all()
    
    @staticmethod
    def full_text_search_sqlite(session: Session, search_term: str) -> List[Article]:
        """
        Full-text search for SQLite using LIKE (basic implementation).
        """
        statement = select(Article).where(
            (Article.title.contains(search_term)) |
            (Article.content.contains(search_term))
        )
        return session.exec(statement).all()
    
    @staticmethod
    def complex_aggregation(session: Session) -> dict:
        """
        Complex aggregation query using SQLAlchemy functions.
        """
        # Using SQLAlchemy's func for aggregations
        statement = select(
            func.count(Article.id).label("total_articles"),
            func.avg(Article.view_count).label("avg_views"),
            func.max(Article.created_at).label("latest_article"),
            func.sum(
                func.case(
                    (Article.is_published == True, 1),
                    else_=0
                )
            ).label("published_count")
        )
        
        result = session.exec(statement).first()
        return {
            "total_articles": result.total_articles,
            "avg_views": float(result.avg_views) if result.avg_views else 0,
            "latest_article": result.latest_article,
            "published_count": result.published_count
        }
    
    @staticmethod
    def window_functions(session: Session) -> List[dict]:
        """
        Using window functions with SQLModel (advanced SQLAlchemy feature).
        """
        # Rank articles by view count within published/unpublished groups
        statement = select(
            Article.id,
            Article.title,
            Article.view_count,
            Article.is_published,
            func.row_number().over(
                partition_by=Article.is_published,
                order_by=Article.view_count.desc()
            ).label("rank_in_group")
        ).order_by(Article.is_published, Article.view_count.desc())
        
        results = session.exec(statement).all()
        return [
            {
                "id": r.id,
                "title": r.title,
                "view_count": r.view_count,
                "is_published": r.is_published,
                "rank_in_group": r.rank_in_group
            }
            for r in results
        ]
    
    @staticmethod
    def custom_sql_function(session: Session) -> List[Article]:
        """
        Using custom SQL functions and raw SQL with SQLModel.
        """
        # Example: Articles created in the last 30 days
        statement = select(Article).where(
            text("created_at > NOW() - INTERVAL '30 days'")  # PostgreSQL
            # For SQLite: text("created_at > datetime('now', '-30 days')")
        )
        return session.exec(statement).all()
    
    @staticmethod
    def subquery_example(session: Session) -> List[Article]:
        """
        Complex subquery example with SQLModel.
        """
        # Find articles with above-average view counts
        avg_views_subquery = select(func.avg(Article.view_count)).scalar_subquery()
        
        statement = select(Article).where(
            Article.view_count > avg_views_subquery
        ).order_by(Article.view_count.desc())
        
        return session.exec(statement).all()


# PostgreSQL specific full-text search setup
def setup_postgresql_fulltext(session: Session):
    """
    Set up PostgreSQL full-text search.
    This shows how to use raw SQL with SQLModel for database-specific features.
    """
    # Create GIN index for full-text search
    session.exec(text("""
        CREATE INDEX IF NOT EXISTS articles_search_idx 
        ON articles USING GIN(search_vector)
    """))
    
    # Create trigger to automatically update search vector
    session.exec(text("""
        CREATE OR REPLACE FUNCTION update_search_vector() 
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.search_vector := to_tsvector('english', 
                COALESCE(NEW.title, '') || ' ' || COALESCE(NEW.content, '')
            );
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    """))
    
    session.exec(text("""
        DROP TRIGGER IF EXISTS update_article_search_vector ON articles;
        CREATE TRIGGER update_article_search_vector
            BEFORE INSERT OR UPDATE ON articles
            FOR EACH ROW EXECUTE FUNCTION update_search_vector();
    """))
    
    session.commit()


# Example usage
def main():
    """Demonstrate advanced SQLAlchemy features with SQLModel."""
    create_tables()
    
    with Session(engine) as session:
        # Create sample articles
        articles = [
            Article(
                title="Introduction to SQLModel",
                content="SQLModel is a library for interacting with SQL databases from Python code, with Python objects.",
                is_published=True,
                view_count=150
            ),
            Article(
                title="Advanced SQLAlchemy Techniques",
                content="Learn how to use complex queries, window functions, and full-text search with SQLAlchemy.",
                is_published=True,
                view_count=89
            ),
            Article(
                title="FastAPI and SQLModel Integration",
                content="Building APIs with FastAPI and SQLModel provides automatic validation and serialization.",
                is_published=False,
                view_count=45
            )
        ]
        
        for article in articles:
            session.add(article)
        session.commit()
        
        # Demonstrate advanced queries
        queries = AdvancedQueries()
        
        print("=== Full-text search (SQLite) ===")
        search_results = queries.full_text_search_sqlite(session, "SQLModel")
        for article in search_results:
            print(f"- {article.title} (views: {article.view_count})")
        
        print("\n=== Complex aggregation ===")
        stats = queries.complex_aggregation(session)
        print(f"Total articles: {stats['total_articles']}")
        print(f"Average views: {stats['avg_views']:.1f}")
        print(f"Published articles: {stats['published_count']}")
        
        print("\n=== Window functions (ranking) ===")
        rankings = queries.window_functions(session)
        for item in rankings:
            status = "Published" if item["is_published"] else "Draft"
            print(f"Rank {item['rank_in_group']} in {status}: {item['title']} ({item['view_count']} views)")
        
        print("\n=== Subquery (above average views) ===")
        popular_articles = queries.subquery_example(session)
        for article in popular_articles:
            print(f"- {article.title}: {article.view_count} views")


if __name__ == "__main__":
    main()
