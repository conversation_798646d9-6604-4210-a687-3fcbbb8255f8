"""
Base model with automatic created_at and updated_at timestamps.
Uses SQLAlchemy 2.0 syntax and best practices.
"""
from datetime import datetime
from typing import Optional
from sqlalchemy import DateTime, Integer, event
from sqlalchemy.orm import Mapped, mapped_column, declarative_base
from sqlalchemy.sql import func

from .database import Base


class TimestampMixin:
    """
    Mixin class that adds created_at and updated_at timestamps.
    These are automatically managed by SQLAlchemy event listeners.
    """
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Timestamp when the record was created"
    )
    
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Timestamp when the record was last updated"
    )


class BaseModel(Base, TimestampMixin):
    """
    Abstract base model that includes:
    - Primary key (id)
    - Automatic created_at and updated_at timestamps
    - Common utility methods
    
    All models should inherit from this class.
    """
    __abstract__ = True
    
    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        autoincrement=True,
        doc="Primary key"
    )
    
    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"
    
    def to_dict(self) -> dict:
        """
        Convert model instance to dictionary.
        Useful for serialization.
        """
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    @classmethod
    def get_table_name(cls) -> str:
        """Get the table name for this model."""
        return cls.__tablename__


# Event listeners to ensure updated_at is always set correctly
@event.listens_for(BaseModel, 'before_update', propagate=True)
def receive_before_update(mapper, connection, target):
    """
    Event listener that automatically updates the updated_at field
    before any update operation.
    """
    target.updated_at = datetime.utcnow()


@event.listens_for(BaseModel, 'before_insert', propagate=True)
def receive_before_insert(mapper, connection, target):
    """
    Event listener that sets created_at and updated_at fields
    before any insert operation.
    """
    now = datetime.utcnow()
    if not hasattr(target, 'created_at') or target.created_at is None:
        target.created_at = now
    if not hasattr(target, 'updated_at') or target.updated_at is None:
        target.updated_at = now
