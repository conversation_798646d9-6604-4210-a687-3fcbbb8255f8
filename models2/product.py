"""
Product model example using SQLAlchemy 2.0 syntax.
"""
from decimal import Decimal
from typing import Optional, List
from sqlalchemy import String, Text, Boolean, Numeric, Foreign<PERSON>ey, Integer
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel


class Product(BaseModel):
    """
    Product model demonstrating various field types and relationships.
    
    Demonstrates:
    - Decimal fields for prices
    - Foreign key relationships
    - Enum-like fields using String with constraints
    - JSON fields (can be added with PostgreSQL)
    """
    __tablename__ = "products"
    
    # Basic product information
    name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        index=True,
        doc="Product name"
    )
    
    slug: Mapped[str] = mapped_column(
        String(200),
        unique=True,
        nullable=False,
        index=True,
        doc="URL-friendly product identifier"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Detailed product description"
    )
    
    short_description: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True,
        doc="Brief product description"
    )
    
    # Pricing
    price: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        nullable=False,
        doc="Product price with 2 decimal places"
    )
    
    sale_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 2),
        nullable=True,
        doc="Sale price if product is on sale"
    )
    
    cost: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 2),
        nullable=True,
        doc="Cost of the product (for profit calculations)"
    )
    
    # Inventory
    stock_quantity: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Current stock quantity"
    )
    
    low_stock_threshold: Mapped[int] = mapped_column(
        Integer,
        default=5,
        nullable=False,
        doc="Threshold for low stock alerts"
    )
    
    # Product attributes
    sku: Mapped[Optional[str]] = mapped_column(
        String(100),
        unique=True,
        nullable=True,
        index=True,
        doc="Stock Keeping Unit identifier"
    )
    
    weight: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(8, 3),
        nullable=True,
        doc="Product weight in kg"
    )
    
    # Status fields
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the product is active and available"
    )
    
    is_featured: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the product is featured"
    )
    
    # Foreign key relationships
    category_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("categories.id"),
        nullable=True,
        doc="ID of the product category"
    )
    
    # Relationships
    # category: Mapped[Optional["Category"]] = relationship(back_populates="products")
    # order_items: Mapped[List["OrderItem"]] = relationship(back_populates="product")
    # tags: Mapped[List["Tag"]] = relationship(
    #     secondary="product_tags",
    #     back_populates="products"
    # )
    
    def __repr__(self) -> str:
        """Enhanced string representation."""
        return f"<Product(id={self.id}, name='{self.name}', price={self.price})>"
    
    @property
    def effective_price(self) -> Decimal:
        """Get the effective price (sale price if available, otherwise regular price)."""
        return self.sale_price if self.sale_price else self.price
    
    @property
    def is_on_sale(self) -> bool:
        """Check if the product is currently on sale."""
        return self.sale_price is not None and self.sale_price < self.price
    
    @property
    def is_low_stock(self) -> bool:
        """Check if the product is low on stock."""
        return self.stock_quantity <= self.low_stock_threshold
    
    @property
    def is_out_of_stock(self) -> bool:
        """Check if the product is out of stock."""
        return self.stock_quantity <= 0
    
    def reduce_stock(self, quantity: int) -> bool:
        """
        Reduce stock quantity by the specified amount.
        Returns True if successful, False if insufficient stock.
        """
        if self.stock_quantity >= quantity:
            self.stock_quantity -= quantity
            return True
        return False
    
    def increase_stock(self, quantity: int) -> None:
        """Increase stock quantity by the specified amount."""
        self.stock_quantity += quantity
