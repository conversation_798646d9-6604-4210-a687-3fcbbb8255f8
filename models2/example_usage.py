#!/usr/bin/env python3
"""
Example usage of the SQLAlchemy 2.0 models with automatic timestamps.

This script demonstrates:
- Creating and using models
- Automatic timestamp management
- Relationships between models
- Business logic methods
- Database operations

Run this script to see the models in action.
"""

import os
from decimal import Decimal
from datetime import datetime

# Set up the database URL for this example (using SQLite for simplicity)
os.environ["DATABASE_URL"] = "sqlite:///./example.db"

from models2 import (
    User, Product, Category, Order, OrderItem, OrderStatus, Tag, Post,
    create_tables, get_db, engine
)


def create_sample_data():
    """Create sample data to demonstrate the models."""
    
    # Create all tables
    print("Creating database tables...")
    create_tables()
    
    with next(get_db()) as db:
        # Create categories
        print("\nCreating categories...")
        electronics = Category(
            name="Electronics",
            slug="electronics",
            description="Electronic devices and gadgets"
        )
        books = Category(
            name="Books",
            slug="books", 
            description="Books and literature"
        )
        
        db.add_all([electronics, books])
        db.commit()
        
        # Create tags
        print("Creating tags...")
        popular_tag = Tag(name="Popular", slug="popular", color="#FF5733")
        new_tag = Tag(name="New Arrival", slug="new-arrival", color="#33FF57")
        sale_tag = Tag(name="On Sale", slug="on-sale", color="#3357FF")
        
        db.add_all([popular_tag, new_tag, sale_tag])
        db.commit()
        
        # Create users
        print("Creating users...")
        user1 = User(
            username="john_doe",
            email="<EMAIL>",
            first_name="John",
            last_name="Doe",
            bio="A regular customer who loves electronics"
        )
        user2 = User(
            username="jane_smith",
            email="<EMAIL>",
            first_name="Jane",
            last_name="Smith",
            is_admin=True
        )
        
        db.add_all([user1, user2])
        db.commit()
        
        # Create products
        print("Creating products...")
        laptop = Product(
            name="Gaming Laptop",
            slug="gaming-laptop",
            description="High-performance gaming laptop with RTX graphics",
            price=Decimal("1299.99"),
            sale_price=Decimal("1199.99"),
            stock_quantity=10,
            sku="LAPTOP-001",
            category_id=electronics.id,
            is_featured=True
        )
        
        book = Product(
            name="Python Programming Guide",
            slug="python-programming-guide",
            description="Comprehensive guide to Python programming",
            price=Decimal("49.99"),
            stock_quantity=25,
            sku="BOOK-001",
            category_id=books.id
        )
        
        db.add_all([laptop, book])
        db.commit()
        
        # Create an order
        print("Creating order...")
        order = Order(
            order_number="ORD-2024-001",
            user_id=user1.id,
            subtotal=Decimal("1249.98"),
            tax_amount=Decimal("125.00"),
            shipping_amount=Decimal("15.00"),
            total_amount=Decimal("1389.98"),
            shipping_address="123 Main St, City, State 12345"
        )
        
        db.add(order)
        db.commit()
        
        # Create order items
        print("Creating order items...")
        order_item1 = OrderItem(
            order_id=order.id,
            product_id=laptop.id,
            quantity=1,
            unit_price=laptop.effective_price,
            product_name=laptop.name,
            product_sku=laptop.sku
        )
        order_item1.calculate_total()
        
        order_item2 = OrderItem(
            order_id=order.id,
            product_id=book.id,
            quantity=1,
            unit_price=book.price,
            product_name=book.name,
            product_sku=book.sku
        )
        order_item2.calculate_total()
        
        db.add_all([order_item1, order_item2])
        db.commit()
        
        # Create a blog post
        print("Creating blog post...")
        post = Post(
            title="Welcome to Our Store",
            slug="welcome-to-our-store",
            content="This is our first blog post about our amazing products!",
            is_published=True,
            author_id=user2.id
        )
        
        db.add(post)
        db.commit()
        
        print("\nSample data created successfully!")
        return {
            'users': [user1, user2],
            'categories': [electronics, books],
            'products': [laptop, book],
            'order': order,
            'tags': [popular_tag, new_tag, sale_tag],
            'post': post
        }


def demonstrate_features():
    """Demonstrate various features of the models."""
    
    with next(get_db()) as db:
        print("\n" + "="*50)
        print("DEMONSTRATING MODEL FEATURES")
        print("="*50)
        
        # Get a user and show timestamps
        user = db.query(User).filter(User.username == "john_doe").first()
        print(f"\nUser: {user}")
        print(f"Created at: {user.created_at}")
        print(f"Updated at: {user.updated_at}")
        print(f"Full name: {user.full_name}")
        
        # Update user and show updated timestamp
        print(f"\nUpdating user bio...")
        original_updated_at = user.updated_at
        user.bio = "Updated bio - I love shopping online!"
        db.commit()
        
        # Refresh to get updated timestamp
        db.refresh(user)
        print(f"Original updated_at: {original_updated_at}")
        print(f"New updated_at: {user.updated_at}")
        print(f"Timestamps are different: {user.updated_at != original_updated_at}")
        
        # Demonstrate product features
        laptop = db.query(Product).filter(Product.sku == "LAPTOP-001").first()
        print(f"\nProduct: {laptop}")
        print(f"Effective price: ${laptop.effective_price}")
        print(f"Is on sale: {laptop.is_on_sale}")
        print(f"Is low stock: {laptop.is_low_stock}")
        
        # Demonstrate order features
        order = db.query(Order).first()
        print(f"\nOrder: {order}")
        print(f"Can be cancelled: {order.can_be_cancelled()}")
        
        # Confirm the order
        order.confirm()
        db.commit()
        print(f"Order status after confirmation: {order.status}")
        print(f"Can be cancelled after confirmation: {order.can_be_cancelled()}")
        
        # Show order items
        order_items = db.query(OrderItem).filter(OrderItem.order_id == order.id).all()
        print(f"\nOrder items:")
        for item in order_items:
            print(f"  - {item}")
        
        # Demonstrate tag features
        tag = db.query(Tag).filter(Tag.name == "Popular").first()
        print(f"\nTag: {tag}")
        tag.increment_usage()
        tag.increment_usage()
        db.commit()
        print(f"Usage count after incrementing: {tag.usage_count}")


def main():
    """Main function to run the example."""
    print("SQLAlchemy 2.0 Models Example")
    print("="*40)
    
    try:
        # Create sample data
        sample_data = create_sample_data()
        
        # Demonstrate features
        demonstrate_features()
        
        print(f"\n" + "="*50)
        print("EXAMPLE COMPLETED SUCCESSFULLY!")
        print("="*50)
        print(f"Database file: example.db")
        print(f"You can inspect the database using any SQLite browser.")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
