"""
Order and OrderItem models demonstrating relationships and complex business logic.
"""
from decimal import Decimal
from enum import Enum
from typing import Optional, List
from sqlalchemy import String, Text, Numeric, ForeignKey, Integer, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel


class OrderStatus(str, Enum):
    """Enum for order status values."""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    PROCESSING = "processing"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"


class Order(BaseModel):
    """
    Order model demonstrating:
    - Enum fields
    - Decimal calculations
    - One-to-many relationships
    - Business logic methods
    """
    __tablename__ = "orders"
    
    # Order identification
    order_number: Mapped[str] = mapped_column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        doc="Unique order number for customer reference"
    )
    
    # Customer information
    user_id: Mapped[int] = mapped_column(
        ForeignKey("users.id"),
        nullable=False,
        doc="ID of the user who placed the order"
    )
    
    # Order status
    status: Mapped[OrderStatus] = mapped_column(
        SQLEnum(OrderStatus),
        default=OrderStatus.PENDING,
        nullable=False,
        doc="Current status of the order"
    )
    
    # Financial information
    subtotal: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        nullable=False,
        doc="Subtotal before taxes and shipping"
    )
    
    tax_amount: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        default=Decimal('0.00'),
        nullable=False,
        doc="Tax amount"
    )
    
    shipping_amount: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        default=Decimal('0.00'),
        nullable=False,
        doc="Shipping cost"
    )
    
    discount_amount: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        default=Decimal('0.00'),
        nullable=False,
        doc="Total discount applied"
    )
    
    total_amount: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        nullable=False,
        doc="Final total amount"
    )
    
    # Shipping information
    shipping_address: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Shipping address"
    )
    
    billing_address: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Billing address"
    )
    
    # Additional information
    notes: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Order notes or special instructions"
    )
    
    # Relationships
    # user: Mapped["User"] = relationship(back_populates="orders")
    # order_items: Mapped[List["OrderItem"]] = relationship(back_populates="order")
    
    def __repr__(self) -> str:
        """Enhanced string representation."""
        return f"<Order(id={self.id}, order_number='{self.order_number}', status='{self.status}', total={self.total_amount})>"
    
    def calculate_total(self) -> Decimal:
        """Calculate and update the total amount."""
        self.total_amount = self.subtotal + self.tax_amount + self.shipping_amount - self.discount_amount
        return self.total_amount
    
    def can_be_cancelled(self) -> bool:
        """Check if the order can be cancelled."""
        return self.status in [OrderStatus.PENDING, OrderStatus.CONFIRMED]
    
    def cancel(self) -> bool:
        """Cancel the order if possible."""
        if self.can_be_cancelled():
            self.status = OrderStatus.CANCELLED
            return True
        return False
    
    def confirm(self) -> None:
        """Confirm the order."""
        if self.status == OrderStatus.PENDING:
            self.status = OrderStatus.CONFIRMED
    
    def ship(self) -> None:
        """Mark the order as shipped."""
        if self.status in [OrderStatus.CONFIRMED, OrderStatus.PROCESSING]:
            self.status = OrderStatus.SHIPPED


class OrderItem(BaseModel):
    """
    Order item model representing individual products in an order.
    """
    __tablename__ = "order_items"
    
    # Foreign keys
    order_id: Mapped[int] = mapped_column(
        ForeignKey("orders.id"),
        nullable=False,
        doc="ID of the order this item belongs to"
    )
    
    product_id: Mapped[int] = mapped_column(
        ForeignKey("products.id"),
        nullable=False,
        doc="ID of the product"
    )
    
    # Item details
    quantity: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Quantity of the product ordered"
    )
    
    unit_price: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        nullable=False,
        doc="Price per unit at the time of order"
    )
    
    total_price: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        nullable=False,
        doc="Total price for this line item (quantity * unit_price)"
    )
    
    # Product snapshot (in case product details change)
    product_name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        doc="Product name at the time of order"
    )
    
    product_sku: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        doc="Product SKU at the time of order"
    )
    
    # Relationships
    # order: Mapped["Order"] = relationship(back_populates="order_items")
    # product: Mapped["Product"] = relationship(back_populates="order_items")
    
    def __repr__(self) -> str:
        """Enhanced string representation."""
        return f"<OrderItem(id={self.id}, product='{self.product_name}', quantity={self.quantity}, total={self.total_price})>"
    
    def calculate_total(self) -> Decimal:
        """Calculate and update the total price for this item."""
        self.total_price = self.unit_price * self.quantity
        return self.total_price
