"""
Category model example using SQLAlchemy 2.0 syntax.
"""
from typing import Optional, List
from sqlalchemy import String, Text, Boolean
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel


class Category(BaseModel):
    """
    Category model for organizing products, posts, etc.
    
    Demonstrates:
    - Hierarchical relationships (parent/child categories)
    - Self-referential foreign keys
    - String fields with different lengths
    """
    __tablename__ = "categories"
    
    # Basic fields
    name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        index=True,
        doc="Category name"
    )
    
    slug: Mapped[str] = mapped_column(
        String(100),
        unique=True,
        nullable=False,
        index=True,
        doc="URL-friendly version of the category name"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Detailed description of the category"
    )
    
    # Hierarchical structure
    parent_id: Mapped[Optional[int]] = mapped_column(
        nullable=True,
        doc="ID of the parent category (for subcategories)"
    )
    
    # Status fields
    is_active: Mapped[bool] = mapped_column(
        <PERSON>olean,
        default=True,
        nullable=False,
        doc="Whether the category is active and visible"
    )
    
    sort_order: Mapped[int] = mapped_column(
        default=0,
        nullable=False,
        doc="Sort order for displaying categories"
    )
    
    # Self-referential relationships
    # parent: Mapped[Optional["Category"]] = relationship(
    #     "Category",
    #     remote_side=[id],
    #     back_populates="children"
    # )
    # 
    # children: Mapped[List["Category"]] = relationship(
    #     "Category",
    #     back_populates="parent"
    # )
    
    # Relationships with other models
    # products: Mapped[List["Product"]] = relationship(back_populates="category")
    
    def __repr__(self) -> str:
        """Enhanced string representation."""
        return f"<Category(id={self.id}, name='{self.name}', slug='{self.slug}')>"
    
    @property
    def is_root_category(self) -> bool:
        """Check if this is a root category (no parent)."""
        return self.parent_id is None
    
    def activate(self) -> None:
        """Activate the category."""
        self.is_active = True
    
    def deactivate(self) -> None:
        """Deactivate the category."""
        self.is_active = False
    
    def get_full_path(self) -> str:
        """
        Get the full path of the category including parent categories.
        This would need to be implemented with proper database queries.
        """
        # This is a placeholder - in a real implementation, you'd
        # recursively fetch parent categories
        return self.name
