"""
User model example using SQLAlchemy 2.0 syntax.
"""
from datetime import datetime
from typing import Optional, List
from sqlalchemy import String, Boolean, DateTime, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel


class User(BaseModel):
    """
    User model with automatic timestamps from BaseModel.
    
    Demonstrates:
    - String fields with constraints
    - Boolean fields
    - Optional fields
    - Relationships (will be defined in other models)
    """
    __tablename__ = "users"
    
    # Required fields
    username: Mapped[str] = mapped_column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        doc="Unique username for the user"
    )
    
    email: Mapped[str] = mapped_column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="User's email address"
    )
    
    # Optional fields with proper typing
    first_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        doc="User's first name"
    )
    
    last_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        doc="User's last name"
    )
    
    # Boolean field with default
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the user account is active"
    )
    
    is_admin: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the user has admin privileges"
    )
    
    # Text field for longer content
    bio: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="User's biography or description"
    )
    
    # Timestamp field (in addition to the automatic ones from BaseModel)
    last_login: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Timestamp of user's last login"
    )
    
    # Relationships will be defined here
    # orders: Mapped[List["Order"]] = relationship(back_populates="user")
    
    def __repr__(self) -> str:
        """Enhanced string representation."""
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    @property
    def full_name(self) -> str:
        """Get the user's full name."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.last_name:
            return self.last_name
        else:
            return self.username
    
    def update_last_login(self) -> None:
        """Update the last login timestamp to now."""
        self.last_login = datetime.utcnow()
    
    def deactivate(self) -> None:
        """Deactivate the user account."""
        self.is_active = False
    
    def activate(self) -> None:
        """Activate the user account."""
        self.is_active = True
