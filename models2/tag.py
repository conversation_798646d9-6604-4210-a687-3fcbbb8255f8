"""
Tag model and many-to-many relationship examples using SQLAlchemy 2.0 syntax.
"""
from typing import Optional, List
from sqlalchemy import String, Text, Boolean, Table, Column, Integer, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel
from .database import Base

# Association table for many-to-many relationship between products and tags
product_tags = Table(
    'product_tags',
    Base.metadata,
    Column('product_id', Integer, ForeignKey('products.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tags.id'), primary_key=True),
    # Add timestamps to the association table if needed
    Column('created_at', nullable=False, server_default='now()'),
)


class Tag(BaseModel):
    """
    Tag model for categorizing and organizing content.
    
    Demonstrates:
    - Many-to-many relationships
    - String fields with constraints
    - Color coding for UI purposes
    """
    __tablename__ = "tags"
    
    # Basic tag information
    name: Mapped[str] = mapped_column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        doc="Tag name (must be unique)"
    )
    
    slug: Mapped[str] = mapped_column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        doc="URL-friendly version of the tag name"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Optional description of what this tag represents"
    )
    
    # Visual customization
    color: Mapped[Optional[str]] = mapped_column(
        String(7),  # For hex color codes like #FF5733
        nullable=True,
        doc="Hex color code for displaying the tag"
    )
    
    # Status and organization
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the tag is active and can be used"
    )
    
    sort_order: Mapped[int] = mapped_column(
        default=0,
        nullable=False,
        doc="Sort order for displaying tags"
    )
    
    # Usage statistics (could be updated via triggers or application logic)
    usage_count: Mapped[int] = mapped_column(
        default=0,
        nullable=False,
        doc="Number of times this tag has been used"
    )
    
    # Many-to-many relationships
    # products: Mapped[List["Product"]] = relationship(
    #     secondary=product_tags,
    #     back_populates="tags"
    # )
    
    def __repr__(self) -> str:
        """Enhanced string representation."""
        return f"<Tag(id={self.id}, name='{self.name}', usage_count={self.usage_count})>"
    
    def increment_usage(self) -> None:
        """Increment the usage count for this tag."""
        self.usage_count += 1
    
    def decrement_usage(self) -> None:
        """Decrement the usage count for this tag (minimum 0)."""
        if self.usage_count > 0:
            self.usage_count -= 1
    
    def activate(self) -> None:
        """Activate the tag."""
        self.is_active = True
    
    def deactivate(self) -> None:
        """Deactivate the tag."""
        self.is_active = False
    
    @classmethod
    def create_from_name(cls, name: str) -> "Tag":
        """
        Create a tag from a name, automatically generating the slug.
        This is a helper method - in practice, you'd use this with a database session.
        """
        import re
        
        # Simple slug generation (you might want to use a proper slugify library)
        slug = re.sub(r'[^\w\s-]', '', name.lower())
        slug = re.sub(r'[-\s]+', '-', slug).strip('-')
        
        return cls(name=name, slug=slug)


# Additional association table example for posts/articles and tags
post_tags = Table(
    'post_tags',
    Base.metadata,
    Column('post_id', Integer, ForeignKey('posts.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tags.id'), primary_key=True),
    Column('created_at', nullable=False, server_default='now()'),
)


class Post(BaseModel):
    """
    Example Post model to demonstrate another many-to-many relationship with tags.
    """
    __tablename__ = "posts"
    
    title: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        doc="Post title"
    )
    
    slug: Mapped[str] = mapped_column(
        String(200),
        unique=True,
        nullable=False,
        index=True,
        doc="URL-friendly post identifier"
    )
    
    content: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Post content"
    )
    
    is_published: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the post is published"
    )
    
    author_id: Mapped[int] = mapped_column(
        ForeignKey("users.id"),
        nullable=False,
        doc="ID of the post author"
    )
    
    # Many-to-many relationship with tags
    # tags: Mapped[List["Tag"]] = relationship(
    #     secondary=post_tags,
    #     back_populates="posts"
    # )
    
    # author: Mapped["User"] = relationship()
    
    def __repr__(self) -> str:
        """Enhanced string representation."""
        return f"<Post(id={self.id}, title='{self.title}', published={self.is_published})>"
