# SQLAlchemy 2.0 Models with Automatic Timestamps

This package provides a comprehensive set of example models using SQLAlchemy 2.0 syntax and best practices, featuring automatic `created_at` and `updated_at` timestamp management.

## Features

- ✅ **Automatic Timestamps**: All models inherit `created_at` and `updated_at` fields that are automatically managed
- ✅ **SQLAlchemy 2.0 Syntax**: Uses the latest `Mapped[]` type annotations and declarative syntax
- ✅ **Event Listeners**: Automatic timestamp updates using SQLAlchemy event system
- ✅ **Relationships**: Demonstrates one-to-many, many-to-many, and self-referential relationships
- ✅ **Business Logic**: Models include useful methods for common operations
- ✅ **Type Safety**: Full type annotations for better IDE support and error catching
- ✅ **Enum Support**: Proper enum usage for status fields
- ✅ **Decimal Fields**: Proper handling of monetary values

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Configure your database URL in the environment or modify `database.py`:
```bash
export DATABASE_URL="postgresql://username:password@localhost:5432/database_name"
```

## Quick Start

```python
from models2 import User, Product, create_tables, get_db

# Initialize database
create_tables()

# Create and use models
with next(get_db()) as db:
    # Create a user - timestamps are automatically set
    user = User(
        username="john_doe",
        email="<EMAIL>",
        first_name="John",
        last_name="Doe"
    )
    db.add(user)
    db.commit()
    
    print(f"User created at: {user.created_at}")
    print(f"User updated at: {user.updated_at}")
    
    # Update the user - updated_at is automatically updated
    user.bio = "I love programming!"
    db.commit()
    
    print(f"User updated at: {user.updated_at}")  # This will be different now
```

## Models Overview

### BaseModel
All models inherit from `BaseModel` which provides:
- `id`: Primary key (auto-increment integer)
- `created_at`: Automatically set when record is created
- `updated_at`: Automatically updated when record is modified
- `to_dict()`: Convert model to dictionary
- `__repr__()`: String representation

### Available Models

1. **User**: User accounts with authentication fields
2. **Category**: Hierarchical categories for organizing content
3. **Product**: Products with pricing, inventory, and relationships
4. **Order & OrderItem**: Order management with line items
5. **Tag**: Tagging system with many-to-many relationships
6. **Post**: Blog posts demonstrating content management

## Automatic Timestamp Management

The timestamp system works through SQLAlchemy event listeners:

```python
# When creating a record
user = User(username="test", email="<EMAIL>")
db.add(user)
db.commit()
# created_at and updated_at are automatically set to now()

# When updating a record
user.email = "<EMAIL>"
db.commit()
# updated_at is automatically updated to now()
# created_at remains unchanged
```

## Relationships Example

```python
# One-to-many relationship
category = Category(name="Electronics", slug="electronics")
product = Product(
    name="Laptop",
    slug="laptop",
    price=Decimal("999.99"),
    category=category  # Relationship
)

# Many-to-many relationship (requires association table)
tag1 = Tag(name="Popular", slug="popular")
tag2 = Tag(name="New", slug="new")
# product.tags.extend([tag1, tag2])  # Would work with proper relationship setup
```

## Business Logic Methods

Models include useful business logic methods:

```python
# Product methods
product = Product(price=Decimal("100.00"), sale_price=Decimal("80.00"))
print(product.effective_price)  # Returns 80.00 (sale price)
print(product.is_on_sale)       # Returns True

# Order methods
order = Order(status=OrderStatus.PENDING)
print(order.can_be_cancelled())  # Returns True
order.confirm()                  # Changes status to CONFIRMED

# User methods
user = User(first_name="John", last_name="Doe")
print(user.full_name)           # Returns "John Doe"
user.update_last_login()        # Sets last_login to now
```

## Database Configuration

### PostgreSQL (Recommended)
```python
DATABASE_URL = "postgresql://username:password@localhost:5432/database_name"
```

### SQLite (Development)
```python
DATABASE_URL = "sqlite:///./app.db"
```

### MySQL
```python
DATABASE_URL = "mysql+pymysql://username:password@localhost:3306/database_name"
```

## Running the Example

Run the example script to see the models in action:

```bash
python models2/example_usage.py
```

This will:
1. Create all database tables
2. Insert sample data
3. Demonstrate automatic timestamp management
4. Show relationships and business logic methods

## Migration Support

For production use, consider using Alembic for database migrations:

```bash
# Initialize Alembic
alembic init alembic

# Create a migration
alembic revision --autogenerate -m "Initial migration"

# Apply migrations
alembic upgrade head
```

## Best Practices

1. **Always use the session context manager** or properly close sessions
2. **Use transactions** for multiple related operations
3. **Validate data** before committing to the database
4. **Use proper type hints** for better IDE support
5. **Keep business logic in model methods** when appropriate
6. **Use enums** for status fields and other constrained values

## Contributing

When adding new models:
1. Inherit from `BaseModel` for automatic timestamps
2. Use proper type annotations with `Mapped[]`
3. Add docstrings for fields and methods
4. Include relevant business logic methods
5. Add examples to the documentation
