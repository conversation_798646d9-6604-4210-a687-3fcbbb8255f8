"""
Database configuration and session management for SQLAlchemy 2.0
"""
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, DeclarativeBase
from sqlalchemy.pool import StaticPool
from typing import Generator
import os

# Database URL - can be configured via environment variable
DATABASE_URL = os.getenv(
    "DATABASE_URL", 
    "postgresql://username:password@localhost:5432/database_name"
)

# For development, you might want to use SQLite:
# DATABASE_URL = "sqlite:///./app.db"

# Create engine with SQLAlchemy 2.0 syntax
engine = create_engine(
    DATABASE_URL,
    echo=True,  # Set to False in production
    future=True,  # Enable SQLAlchemy 2.0 mode
    # For SQLite only:
    # connect_args={"check_same_thread": False},
    # poolclass=StaticPool,
)

# Create session factory
SessionLocal = sessionmaker(
    bind=engine,
    autocommit=False,
    autoflush=False,
    future=True  # Enable SQLAlchemy 2.0 mode
)


class Base(DeclarativeBase):
    """
    Base class for all SQLAlchemy models.
    Uses the new SQLAlchemy 2.0 declarative syntax.
    """
    pass


def get_db() -> Generator:
    """
    Dependency function to get database session.
    Use this in your application to get a database session.
    
    Example usage:
        with get_db() as db:
            user = db.query(User).first()
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """
    Create all tables in the database.
    Call this function to initialize your database schema.
    """
    Base.metadata.create_all(bind=engine)


def drop_tables():
    """
    Drop all tables in the database.
    Use with caution!
    """
    Base.metadata.drop_all(bind=engine)
