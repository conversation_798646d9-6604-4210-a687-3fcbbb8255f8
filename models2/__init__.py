"""
SQLAlchemy 2.0 Models Package

This package contains a comprehensive set of example models demonstrating
SQLAlchemy 2.0 best practices, including:

- Automatic created_at and updated_at timestamps
- Proper type annotations with Mapped[]
- Relationships between models
- Business logic methods
- Enum usage
- Many-to-many relationships

Usage:
    from models2 import User, Product, Order, Category, Tag
    from models2.database import create_tables, get_db

    # Initialize database
    create_tables()

    # Use in your application
    with get_db() as db:
        user = User(username="john_doe", email="<EMAIL>")
        db.add(user)
        db.commit()
"""

# Import the base classes
from .database import Base, engine, SessionLocal, get_db, create_tables, drop_tables
from .base import BaseModel, TimestampMixin

# Import all models
from .user import User
from .category import Category
from .product import Product
from .order import Order, OrderItem, OrderStatus
from .tag import Tag, Post, product_tags, post_tags

# Define what gets imported with "from models2 import *"
__all__ = [
    # Database utilities
    "Base",
    "engine",
    "SessionLocal",
    "get_db",
    "create_tables",
    "drop_tables",

    # Base classes
    "BaseModel",
    "TimestampMixin",

    # Models
    "User",
    "Category",
    "Product",
    "Order",
    "OrderItem",
    "OrderStatus",
    "Tag",
    "Post",

    # Association tables
    "product_tags",
    "post_tags",
]

# Package metadata
__version__ = "1.0.0"
__author__ = "Your Name"
__description__ = "SQLAlchemy 2.0 models with automatic timestamps"