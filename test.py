# import json
# # from pornhub_api import PornhubApi
# import requests
# import sys
# from yt_dlp.extractor.pornhub import PornHubIE
# # import yt_dlp
# # sys.path.append('./xxxparser')
# from xxxparser import pornhub
# # from xxxparser
#
# # def get_video_info(url):
# #     ydl_opts = {
# #         # any other options you need, e.g. 'format': 'bestaudio/best'
# #     }
# #     with yt_dlp.YoutubeDL(ydl_opts) as ydl:
# #         # Extract info only, do not download
# #         info = ydl.extract_info(url, download=False)
# #         # Make it JSON-serializable
# #         info_json = ydl.sanitize_info(info)
# #     return info_json

import requests
import json
import re

def get_video_info(video_id):
    """
    Get basic video information using the video ID
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    url = f"https://www.pornhub.com/webmasters/video_by_id?id={video_id}"

    try:
        response = requests.get(url, headers=headers)
        response. aise_for_status()
        return response.json()
    except requests.RequestException as e:
        return {"error": str(e)}

def extract_video_id(url):
    """
    Extract video ID from a Pornhub URL
    """
    pattern = r'viewkey=([a-zA-Z0-9]+)'
    match = re.search(pattern, url)
    return match.group(1) if match else None

if __name__ == "__main__":
    # Example usage
    url = "https://www.pornhub.com/view_video.php?viewkey=680f7f4e082cf"
    video_id = extract_video_id(url)

    if video_id:
        info = get_video_info(video_id)
        print(json.dumps(info, indent=2))
    else:
        print("Could not extract video ID from URL")
#
# if __name__=="__main__":
#     pornhub.get_video_info("https://www.pornhub.com/view_video.php?viewkey=6817d47ee87f6")
#     api = PornhubApi()
#     videos = api.search_videos.search_videos(
#         "chechick",
#         ordering="mostviewed",
#         period="weekly",
#         tags=["black"],
#     )
#     for vid in videos:
#         print(vid.title, vid.video_id)
#
#     url = "https://www.pornhub.com/view_video.php?viewkey=6822d54cd5f62"
#     info = get_video_info(url)
#     print(json.dumps(info, indent=2))
#
#     s = requests.Session()
#     # vid_url_links = pornhub.parse_pornhub_url(s, url="https://www.pornhub.com/view_video.php?viewkey=6822d54cd5f62", domain="pornhub.com")
#     page = pornhub.load_ph_page("https://www.pornhub.com/view_video.php?viewkey=6822d54cd5f62", s, max_tries=2)
#     pornhub.
#
#     print(page)
#     # yt = yt_dlp.
#     #
#     # phub_ie = PornHubIE()
#     # phe = phub_ie.extract()
#     # print(phe.get('title'))
#
#
