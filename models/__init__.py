from playhouse.postgres_ext import IntervalField

import datetime
from peewee import *

db = SqliteDatabase('my_app.db')

class BaseModel(Model):
    class Meta:
        database = db

class PornhubTag(BaseModel):
    name = CharField(unique=True)
    url = CharField()
    videos = ManyToManyField(PornhubVideo, backref='tags')

class PornhubCategory(BaseModel):
    name = CharField(unique=True)
    url = CharField()
    videos = ManyToManyField(PornhubVideo, backref='categories')

class PornhubVideo(BaseModel):
    viewkey = CharField(unique=True, primary_key=True)
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)
    video_uploaded_at = DateTimeField()
    url= CharField(unique=True)
    title = CharField()
    likes = IntegerField(default=0)
    views = IntegerField(default=0)
    processed = BooleanField(default=False)
    # favorited = BooleanField(default=False)
    dislikes = IntegerField(default=0)
    duration = IntegerField()
    metadata = TextField()
    comments = TextField()
    tags = ManyToManyField(Tag, backref='videos')
    categories = ManyToManyField(Category, backref='videos')
    performers = ManyToManyField(Performer, backref='videos')

class PornhubPerformer(BaseModel):
    url = CharField(unique=True, primary_key=True)
    name = CharField()
    bio = TextField()
    performer_type = CharField()
    pornhub_url = CharField()
    avatar = CharField()
    desc = TextField(null=True)
    gender = CharField(null=True)
    ethnicity = CharField(null=True)
    fake_boobs = BooleanField(null=True)
    hair_color = CharField(null=True)
    height = CharField(null=True)
    interested_in = CharField(null=True)
    interests_and_hobbies = TextField(null=True)
    piercings = BooleanField(null=True)
    weight = CharField(null=True)
    relationship_status = CharField(null=True)
    tattoos = BooleanField(null=True)
    turn_ons = BooleanField(null=True)
    profile_views = IntegerField(default=0)
    video_views = IntegerField(default=0)
    url = CharField(unique=True)
    title = CharField()
    duration = IntegerField()
    metadata = TextField()
    comments = TextField()
    tags = ManyToManyField(Tag)
    categories = ManyToManyField(Category)
